#!/usr/bin/env python3
"""
高性能BOSS直聘爬虫 - 专注速度和准确性
目标：5页数据在5秒内完成爬取
"""

import asyncio
import json
import time
import random
from typing import List, Dict, Any, Optional
from urllib.parse import quote
from concurrent.futures import ThreadPoolExecutor
import aiohttp
from datetime import datetime

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

from data_models import JobPosition, JobSearchResult, CrawlerConfig
from utils import Logger, DataExporter


class FastAntiDetection:
    """增强型反检测技术"""

    @staticmethod
    def get_enhanced_stealth_js() -> str:
        """增强型隐身JavaScript"""
        return """
        // 完整的反检测代码
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
        Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
        Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en']});

        // 隐藏自动化特征
        delete navigator.__proto__.webdriver;
        Object.defineProperty(navigator, 'permissions', {get: () => undefined});
        Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
        Object.defineProperty(navigator, 'userAgent', {
            get: () => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        });

        // 模拟真实用户行为
        const originalQuery = window.document.querySelector;
        window.document.querySelector = function(selector) {
            return originalQuery.call(document, selector);
        };

        // 添加随机鼠标移动
        function simulateMouseMove() {
            const event = new MouseEvent('mousemove', {
                clientX: Math.random() * window.innerWidth,
                clientY: Math.random() * window.innerHeight
            });
            document.dispatchEvent(event);
        }

        // 定期模拟鼠标移动
        setInterval(simulateMouseMove, 2000 + Math.random() * 3000);

        // 等待页面完全加载
        await new Promise(resolve => {
            if (document.readyState === 'complete') {
                setTimeout(resolve, 2000 + Math.random() * 3000);
            } else {
                window.addEventListener('load', () => {
                    setTimeout(resolve, 2000 + Math.random() * 3000);
                });
            }
        });
        """
    
    @staticmethod
    def get_fast_browser_config() -> BrowserConfig:
        """快速浏览器配置"""
        return BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-web-security",
                "--disable-blink-features=AutomationControlled",
                "--disable-features=VizDisplayCompositor",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-field-trial-config",
                "--disable-back-forward-cache",
                "--disable-ipc-flooding-protection",
                "--enable-features=NetworkService,NetworkServiceInProcess",
                "--force-color-profile=srgb",
                "--metrics-recording-only",
                "--use-mock-keychain",
                # 增强反检测
                "--exclude-switches=enable-automation",
                "--disable-blink-features=AutomationControlled",
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling",
            ]
        )


class FastJobExtractor:
    """快速职位信息提取器"""
    
    @staticmethod
    def get_extraction_strategy() -> JsonCssExtractionStrategy:
        """获取优化的提取策略 - 适配最新BOSS直聘页面结构"""
        schema = {
            "name": "boss_jobs",
            # 更新基础选择器，覆盖更多可能的页面结构
            "baseSelector": ".job-list-box .job-card-wrapper, .job-list .job-card-left, li[ka*='search_list'], .job-list-item, .job-card-body, .job-primary",
            "fields": [
                {
                    "name": "job_title",
                    # 扩展职位标题选择器
                    "selector": ".job-name a, .job-title, .job-name, .job-title a, span[class*='job-name'], .job-card-left .job-name, .job-primary .job-name",
                    "type": "text"
                },
                {
                    "name": "salary_range",
                    # 扩展薪资选择器
                    "selector": ".salary, .red, .job-limit .red, span[class*='salary'], .job-card-left .salary, .job-primary .salary",
                    "type": "text"
                },
                {
                    "name": "company_name",
                    # 扩展公司名称选择器
                    "selector": ".company-name a, .company-name, .company-text, span[class*='company-name'], .job-card-left .company-name, .job-company .company-name",
                    "type": "text"
                },
                {
                    "name": "job_detail_url",
                    # 扩展详情链接选择器
                    "selector": ".job-name a, .job-title a, a[ka*='search_list'], .job-card-left .job-name a, .job-primary .job-name a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "location",
                    # 扩展地点选择器
                    "selector": ".job-area, .job-limit-tips em:first-child, .job-area-wrapper, span[class*='job-area'], .job-card-left .job-area, .job-limit .job-area",
                    "type": "text"
                },
                {
                    "name": "work_experience",
                    # 扩展工作经验选择器
                    "selector": ".job-limit-tips em:nth-child(2), .job-limit .limit-item:first-child, .job-detail .job-limit em, span[class*='experience']",
                    "type": "text"
                },
                {
                    "name": "education",
                    # 扩展学历要求选择器
                    "selector": ".job-limit-tips em:nth-child(3), .job-limit .limit-item:last-child, .job-detail .job-limit em:last-child, span[class*='education']",
                    "type": "text"
                },
                {
                    "name": "company_scale",
                    # 扩展公司规模选择器
                    "selector": ".company-tag-list li:first-child, .company-info p, .company-tag-list .tag-item, span[class*='company-scale']",
                    "type": "text"
                },
                {
                    "name": "company_industry",
                    "selector": ".company-tag-list li:last-child, .company-info p:last-child",
                    "type": "text"
                }
            ]
        }
        return JsonCssExtractionStrategy(schema)


class FastBossCrawler:
    """高性能BOSS直聘爬虫"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.crawler = None
        self.session = None
        self.start_time = None
        
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def initialize(self):
        """快速初始化"""
        Logger.info("⚡ 启动高性能爬虫...")
        self.start_time = time.time()



        # 创建增强型浏览器配置
        browser_config = FastAntiDetection.get_fast_browser_config()

        # 创建爬虫实例
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()

        # 创建HTTP会话
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            headers={
                'User-Agent': browser_config.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        )

        Logger.success("✅ 高性能爬虫初始化完成")
    
    async def close(self):
        """关闭资源"""
        if self.crawler:
            await self.crawler.__aexit__(None, None, None)
        if self.session:
            await self.session.close()

        if self.start_time:
            total_time = time.time() - self.start_time
            Logger.info(f"⏱️ 总执行时间: {total_time:.2f}秒")


    
    def generate_search_urls(self, keyword: str, location: str, max_pages: int) -> List[str]:
        """生成搜索URL列表"""
        # 地点映射
        location_map = {
            "北京": "101010100",
            "上海": "101020100", 
            "深圳": "101280600",
            "杭州": "101210100",
            "广州": "101280100",
            "成都": "101270100",
            "南京": "101190400",
            "武汉": "101200100",
            "西安": "101110100",
            "苏州": "101190400"
        }
        
        city_code = location_map.get(location, "101010100")  # 默认北京
        encoded_keyword = quote(keyword)
        
        urls = []
        for page in range(1, max_pages + 1):
            url = f"https://www.zhipin.com/web/geek/job?query={encoded_keyword}&city={city_code}&page={page}"
            urls.append(url)
        
        return urls
    
    async def crawl_single_page_fast(self, url: str, page_num: int) -> List[JobPosition]:
        """快速爬取单页"""
        try:
            # 配置快速运行参数
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=15000,  # 增加到15秒超时
                delay_before_return_html=3.0,  # 增加等待时间让JS执行
                js_code=[FastAntiDetection.get_enhanced_stealth_js()],
                extraction_strategy=FastJobExtractor.get_extraction_strategy(),
                wait_for_images=False,
                screenshot=False,
                simulate_user=False,  # 关闭用户模拟以提升速度
                override_navigator=False  # 关闭导航器覆盖
            )

            Logger.info(f"🚀 爬取第{page_num}页")

            result = await self.crawler.arun(url=url, config=run_config)

            if result.success and result.extracted_content:
                try:
                    data = json.loads(result.extracted_content)
                    jobs = []

                    # 并行处理数据
                    for item in data:
                        if self._is_valid_job_data(item):
                            job = self._create_job_position(item)
                            jobs.append(job)

                    Logger.success(f"✅ 第{page_num}页完成，获取{len(jobs)}个职位")
                    return jobs

                except json.JSONDecodeError as e:
                    Logger.warning(f"⚠️ 第{page_num}页JSON解析失败，尝试备用解析")
                    # 备用解析策略
                    return await self._fallback_parse(result.html, page_num)
            else:
                Logger.warning(f"⚠️ 第{page_num}页爬取失败，尝试备用方法")
                return await self._fallback_crawl(url, page_num)

        except Exception as e:
            Logger.error(f"❌ 第{page_num}页爬取异常: {e}")
            return []

    async def _fallback_parse(self, html: str, page_num: int) -> List[JobPosition]:
        """备用HTML解析"""
        try:
            import re
            from bs4 import BeautifulSoup

            soup = BeautifulSoup(html, 'html.parser')
            jobs = []

            # 查找职位卡片
            job_cards = soup.find_all(['li', 'div'], class_=re.compile(r'job.*card|job.*item'))

            for card in job_cards[:20]:  # 限制数量提升速度
                try:
                    # 提取基本信息
                    title_elem = card.find(['a', 'span'], class_=re.compile(r'job.*name|job.*title'))
                    company_elem = card.find(['a', 'span'], class_=re.compile(r'company.*name'))
                    salary_elem = card.find(['span', 'div'], class_=re.compile(r'salary|red'))

                    if title_elem and company_elem:
                        job = JobPosition(
                            job_title=title_elem.get_text(strip=True),
                            job_requirements="",
                            salary_range=salary_elem.get_text(strip=True) if salary_elem else "",
                            company_name=company_elem.get_text(strip=True),
                            job_detail_url=title_elem.get('href', '') if title_elem.name == 'a' else "",
                            crawl_time=datetime.now()
                        )
                        jobs.append(job)
                except:
                    continue

            Logger.info(f"🔄 第{page_num}页备用解析获取{len(jobs)}个职位")
            return jobs

        except Exception as e:
            Logger.warning(f"⚠️ 第{page_num}页备用解析失败: {e}")
            return []

    async def _fallback_crawl(self, url: str, page_num: int) -> List[JobPosition]:
        """备用爬取方法"""
        try:
            # 使用aiohttp直接请求
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    return await self._fallback_parse(html, page_num)
        except:
            pass
        return []
    
    def _is_valid_job_data(self, item: Dict[str, Any]) -> bool:
        """验证职位数据有效性"""
        required_fields = ['job_title', 'company_name']
        return all(item.get(field) and str(item.get(field)).strip() for field in required_fields)
    
    def _create_job_position(self, item: Dict[str, Any]) -> JobPosition:
        """创建职位对象"""
        # 处理URL
        job_url = item.get('job_detail_url', '')
        if job_url and not job_url.startswith('http'):
            job_url = f"https://www.zhipin.com{job_url}"
        
        return JobPosition(
            job_title=str(item.get('job_title', '')).strip(),
            job_requirements=f"{item.get('work_experience', '')} {item.get('education', '')}".strip(),
            salary_range=str(item.get('salary_range', '')).strip(),
            company_name=str(item.get('company_name', '')).strip(),
            company_scale=str(item.get('company_scale', '')).strip() or None,
            company_industry=str(item.get('company_industry', '')).strip() or None,
            job_detail_url=job_url,
            location=str(item.get('location', '')).strip() or None,
            work_experience=str(item.get('work_experience', '')).strip() or None,
            education=str(item.get('education', '')).strip() or None,
            job_description=None,  # 快速模式不获取详情
            welfare_benefits=None,
            hr_info=None,
            publish_time=None,
            crawl_time=datetime.now()
        )
    
    async def fast_crawl(self, keyword: str, location: str, max_pages: int) -> JobSearchResult:
        """高性能并发爬取"""
        Logger.info(f"⚡ 开始高性能爬取: {keyword} @ {location}, {max_pages}页")

        # 生成所有URL
        urls = self.generate_search_urls(keyword, location, max_pages)

        # 动态调整并发数
        optimal_concurrency = min(max_pages, 5)  # 最多5个并发，根据页数调整
        semaphore = asyncio.Semaphore(optimal_concurrency)

        async def crawl_with_semaphore(url: str, page_num: int):
            async with semaphore:
                return await self.crawl_single_page_fast(url, page_num)

        # 并发爬取所有页面
        tasks = []
        for i, url in enumerate(urls, 1):
            task = crawl_with_semaphore(url, i)
            tasks.append(task)

        # 等待所有任务完成，动态超时
        timeout = max(10, max_pages * 2)  # 动态超时：至少10秒，每页2秒
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            Logger.warning(f"⚠️ 爬取超时({timeout}秒)，使用已获取的数据")
            results = []

        # 合并结果
        all_jobs = []
        successful_pages = 0
        for result in results:
            if isinstance(result, list):
                all_jobs.extend(result)
                if result:  # 如果有数据则认为成功
                    successful_pages += 1
            elif isinstance(result, Exception):
                Logger.warning(f"⚠️ 任务异常: {result}")

        Logger.info(f"📊 成功爬取 {successful_pages}/{max_pages} 页，获取 {len(all_jobs)} 个原始职位")

        # 快速去重和排序
        unique_jobs = self._fast_deduplication(all_jobs)

        # 按薪资排序（可选）
        unique_jobs = self._sort_jobs_by_salary(unique_jobs)

        Logger.success(f"✅ 去重后获得 {len(unique_jobs)} 个唯一职位")

        # 创建结果
        search_result = JobSearchResult(
            total_count=len(unique_jobs),
            current_page=max_pages,
            jobs=unique_jobs,
            search_keyword=keyword,
            search_location=location
        )

        # 快速导出
        await self._fast_export(search_result)

        return search_result
    
    def _fast_deduplication(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """快速去重算法"""
        seen = set()
        unique_jobs = []

        for job in jobs:
            # 使用职位标题+公司名称作为唯一标识
            key = f"{job.job_title}_{job.company_name}".lower()
            if key not in seen:
                seen.add(key)
                unique_jobs.append(job)

        return unique_jobs

    def _sort_jobs_by_salary(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """按薪资排序"""
        def extract_salary_num(salary_str: str) -> int:
            """提取薪资数字用于排序"""
            if not salary_str:
                return 0
            import re
            numbers = re.findall(r'\d+', salary_str)
            if numbers:
                return int(numbers[0])
            return 0

        try:
            return sorted(jobs, key=lambda x: extract_salary_num(x.salary_range), reverse=True)
        except:
            return jobs
    
    async def _fast_export(self, result: JobSearchResult):
        """快速导出数据"""
        if not result.jobs:
            return
        
        try:
            # 检查是否需要导出所有格式
            export_all = getattr(self.config, 'export_all_formats', False)
            
            if export_all or self.config.output_format == "all":
                # 并发导出多种格式
                tasks = []
                
                # JSON格式
                json_file = self.config.output_file.replace('.json', '').replace('.csv', '').replace('.xlsx', '') + '.json'
                tasks.append(DataExporter.export_to_json(result, json_file))
                
                # Excel格式
                excel_file = self.config.output_file.replace('.json', '').replace('.csv', '').replace('.xlsx', '') + '.xlsx'
                tasks.append(asyncio.to_thread(DataExporter.export_to_excel, result, excel_file))
                
                await asyncio.gather(*tasks, return_exceptions=True)
                
            else:
                # 单一格式导出
                if self.config.output_format == "json":
                    await DataExporter.export_to_json(result, self.config.output_file)
                elif self.config.output_format == "excel":
                    await asyncio.to_thread(DataExporter.export_to_excel, result, self.config.output_file)
                elif self.config.output_format == "csv":
                    await asyncio.to_thread(DataExporter.export_to_csv, result, self.config.output_file)
                    
        except Exception as e:
            Logger.error(f"❌ 导出失败: {e}")


# 便捷函数
async def fast_crawl_boss_jobs(
    keyword: str = "Python开发",
    location: str = "北京", 
    max_pages: int = 5,
    output_format: str = "all",
    output_file: str = "fast_boss_jobs.json",
    headless: bool = True
) -> JobSearchResult:
    """高性能职位爬取函数"""
    
    config = CrawlerConfig(
        search_keyword=keyword,
        search_location=location,
        max_pages=max_pages,
        output_format=output_format,
        output_file=output_file,
        headless=headless,
        timeout=30,  # 减少超时时间
        delay_range=(0.5, 1.0),  # 大幅减少延时
        max_retries=2  # 减少重试次数
    )
    config.export_all_formats = (output_format == "all")
    
    async with FastBossCrawler(config) as crawler:
        return await crawler.fast_crawl(keyword, location, max_pages)


if __name__ == "__main__":
    # 测试函数
    async def test_fast_crawler():
        result = await fast_crawl_boss_jobs("Python开发", "北京", 5)
        print(f"爬取完成，获取{len(result.jobs)}个职位")
    
    asyncio.run(test_fast_crawler())
