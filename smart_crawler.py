#!/usr/bin/env python3
"""
智能BOSS直聘爬虫 - 自适应反检测系统
"""

import asyncio
import json
import time
import random
import os
from typing import List, Dict, Any, Optional
from urllib.parse import quote
import aiohttp
from datetime import datetime, timedelta
import pickle

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from data_models import JobPosition, JobSearchResult, CrawlerConfig
from utils import Logger, DataExporter


class SmartAntiDetection:
    """智能反检测系统"""
    
    @staticmethod
    def get_random_user_agent() -> str:
        """获取随机User-Agent"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        ]
        return random.choice(user_agents)
    
    @staticmethod
    def get_smart_browser_config() -> BrowserConfig:
        """智能浏览器配置"""
        return BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=random.choice([1366, 1920, 1440, 1536]),
            viewport_height=random.choice([768, 1080, 900, 864]),
            user_agent=SmartAntiDetection.get_random_user_agent(),
            extra_args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions-file-access-check",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "--disable-features=VizDisplayCompositor",
                "--disable-ipc-flooding-protection",
                "--no-first-run",
                "--no-service-autorun",
                "--password-store=basic",
                "--use-mock-keychain",
            ]
        )
    
    @staticmethod
    def get_advanced_stealth_js() -> str:
        """高级隐身JavaScript"""
        return """
        // 高级反检测代码
        (function() {
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                configurable: true
            });
            
            // 修改plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
                configurable: true
            });
            
            // 修改languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en-US', 'en'],
                configurable: true
            });
            
            // 修改platform
            Object.defineProperty(navigator, 'platform', {
                get: () => 'Win32',
                configurable: true
            });
            
            // 隐藏自动化特征
            delete navigator.__proto__.webdriver;
            
            // 模拟真实的鼠标和键盘事件
            const originalAddEventListener = EventTarget.prototype.addEventListener;
            EventTarget.prototype.addEventListener = function(type, listener, options) {
                return originalAddEventListener.call(this, type, listener, options);
            };
            
            // 模拟真实用户行为
            function simulateRealUser() {
                // 随机滚动
                const scrollSteps = 2 + Math.floor(Math.random() * 3);
                const scrollHeight = Math.max(
                    document.body.scrollHeight,
                    document.documentElement.scrollHeight
                );
                
                for (let i = 0; i < scrollSteps; i++) {
                    setTimeout(() => {
                        const scrollTo = Math.random() * scrollHeight * 0.7;
                        window.scrollTo({
                            top: scrollTo,
                            behavior: 'smooth'
                        });
                    }, 1000 + Math.random() * 2000);
                }
                
                // 模拟鼠标移动
                setTimeout(() => {
                    for (let i = 0; i < 3; i++) {
                        setTimeout(() => {
                            const event = new MouseEvent('mousemove', {
                                clientX: Math.random() * window.innerWidth,
                                clientY: Math.random() * window.innerHeight,
                                bubbles: true
                            });
                            document.dispatchEvent(event);
                        }, i * 500);
                    }
                }, 2000);
                
                // 模拟页面停留
                setTimeout(() => {
                    const event = new Event('focus');
                    window.dispatchEvent(event);
                }, 3000);
            }
            
            // 页面加载完成后执行
            if (document.readyState === 'complete') {
                setTimeout(simulateRealUser, 1500 + Math.random() * 2000);
            } else {
                window.addEventListener('load', () => {
                    setTimeout(simulateRealUser, 1500 + Math.random() * 2000);
                });
            }
        })();
        """


class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.session_file = "crawler_session.pkl"
        self.last_request_time = 0
        self.request_count = 0
        self.session_data = self.load_session()
    
    def load_session(self) -> dict:
        """加载会话数据"""
        if os.path.exists(self.session_file):
            try:
                with open(self.session_file, 'rb') as f:
                    return pickle.load(f)
            except:
                pass
        return {
            'cookies': {},
            'headers': {},
            'last_success_time': None,
            'failure_count': 0
        }
    
    def save_session(self):
        """保存会话数据"""
        try:
            with open(self.session_file, 'wb') as f:
                pickle.dump(self.session_data, f)
        except Exception as e:
            Logger.warning(f"保存会话失败: {e}")
    
    def should_wait(self) -> float:
        """计算需要等待的时间"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # 基础延时
        base_delay = 8 + random.uniform(3, 8)
        
        # 根据失败次数增加延时
        failure_penalty = self.session_data.get('failure_count', 0) * 2
        
        # 根据请求频率增加延时
        if time_since_last < 10:
            frequency_penalty = 5
        else:
            frequency_penalty = 0
        
        total_delay = base_delay + failure_penalty + frequency_penalty
        return min(total_delay, 60)  # 最大60秒
    
    def record_request(self, success: bool):
        """记录请求结果"""
        self.last_request_time = time.time()
        self.request_count += 1
        
        if success:
            self.session_data['last_success_time'] = datetime.now()
            self.session_data['failure_count'] = 0
        else:
            self.session_data['failure_count'] += 1
        
        self.save_session()


class SmartBossCrawler:
    """智能BOSS直聘爬虫"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.crawler = None
        self.session_manager = SessionManager()
        self.start_time = None
        
    async def __aenter__(self):
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        
    async def initialize(self):
        """初始化智能爬虫"""
        Logger.info("🧠 启动智能爬虫...")
        
        # 智能浏览器配置
        browser_config = SmartAntiDetection.get_smart_browser_config()
        
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()
        
        Logger.success("✅ 智能爬虫初始化完成")
        
    async def cleanup(self):
        """清理资源"""
        if self.crawler:
            await self.crawler.__aexit__(None, None, None)
            
    async def smart_crawl_page(self, url: str, page_num: int) -> List[JobPosition]:
        """智能爬取单页"""
        try:
            # 智能等待
            wait_time = self.session_manager.should_wait()
            Logger.info(f"🧠 智能等待 {wait_time:.1f}秒 后爬取第{page_num}页")
            await asyncio.sleep(wait_time)
            
            # 配置智能运行参数
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=45000,  # 45秒超时
                delay_before_return_html=8.0,  # 等待8秒
                js_code=[SmartAntiDetection.get_advanced_stealth_js()],
                wait_for_images=False,
                screenshot=False,
                simulate_user=True,
                override_navigator=True,
                headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Cache-Control': 'max-age=0',
                }
            )
            
            Logger.info(f"🧠 智能爬取第{page_num}页")
            result = await self.crawler.arun(url=url, config=run_config)
            
            if result.success:
                # 检查页面内容
                if self._is_verification_page(result.html):
                    Logger.warning(f"⚠️ 第{page_num}页遇到验证，记录失败")
                    self.session_manager.record_request(False)
                    return []
                
                # 解析职位数据
                jobs = await self._smart_parse_jobs(result.html, page_num)
                
                if jobs:
                    Logger.success(f"✅ 第{page_num}页成功，获取{len(jobs)}个职位")
                    self.session_manager.record_request(True)
                else:
                    Logger.warning(f"⚠️ 第{page_num}页无数据")
                    self.session_manager.record_request(False)
                
                return jobs
            else:
                Logger.warning(f"⚠️ 第{page_num}页请求失败")
                self.session_manager.record_request(False)
                return []
                
        except Exception as e:
            Logger.error(f"❌ 第{page_num}页异常: {e}")
            self.session_manager.record_request(False)
            return []
    
    def _is_verification_page(self, html: str) -> bool:
        """检查是否为验证页面"""
        verification_keywords = [
            "验证", "captcha", "滑动", "点击完成", "身份验证",
            "异常访问", "verify", "challenge"
        ]
        return any(keyword in html for keyword in verification_keywords)
    
    async def _smart_parse_jobs(self, html: str, page_num: int) -> List[JobPosition]:
        """智能解析职位数据"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            jobs = []
            
            # 多种选择器策略
            selectors = [
                '.job-card-wrapper',
                '.job-card-left', 
                'li[ka*="search_list"]',
                '.job-list-item',
                '.job-card-body',
                '.job-primary',
                '[class*="job-card"]',
                '[class*="job-item"]'
            ]
            
            job_elements = []
            for selector in selectors:
                elements = soup.select(selector)
                if elements and len(elements) > 3:  # 至少要有3个元素才认为有效
                    job_elements = elements
                    Logger.info(f"🎯 使用选择器: {selector}, 找到{len(elements)}个元素")
                    break
            
            if not job_elements:
                Logger.warning(f"⚠️ 第{page_num}页未找到职位元素")
                return []
            
            for element in job_elements[:20]:  # 限制每页最多20个
                job = self._extract_job_info(element)
                if job:
                    jobs.append(job)
            
            return jobs
            
        except Exception as e:
            Logger.warning(f"⚠️ 第{page_num}页解析失败: {e}")
            return []
    
    def _extract_job_info(self, element) -> Optional[JobPosition]:
        """提取职位信息"""
        try:
            # 职位标题
            title_selectors = [
                '.job-name a', '.job-title', '.job-name', 
                'h3 a', '[class*="job-name"]', '[class*="job-title"]'
            ]
            job_title = self._extract_text(element, title_selectors)
            
            if not job_title or len(job_title.strip()) < 2:
                return None
            
            # 薪资
            salary_selectors = [
                '.salary', '.red', '[class*="salary"]', 
                '[class*="money"]', '[class*="pay"]'
            ]
            salary = self._extract_text(element, salary_selectors) or "面议"
            
            # 公司名称
            company_selectors = [
                '.company-name a', '.company-name', '[class*="company-name"]',
                '[class*="company"]'
            ]
            company = self._extract_text(element, company_selectors) or "未知公司"
            
            # 地点
            location_selectors = [
                '.job-area', '[class*="area"]', '[class*="location"]'
            ]
            location = self._extract_text(element, location_selectors) or "北京"
            
            return JobPosition(
                job_title=job_title.strip(),
                salary_range=salary.strip(),
                company_name=company.strip(),
                location=location.strip(),
                work_experience="不限",
                education="不限",
                job_detail_url="",
                company_scale="未知",
                company_industry="未知",
                job_description="",
                hr_info=None,
                publish_time=None,
                crawl_time=datetime.now()
            )
            
        except Exception as e:
            return None
    
    def _extract_text(self, element, selectors: List[str]) -> Optional[str]:
        """从元素中提取文本"""
        for selector in selectors:
            try:
                elem = element.select_one(selector)
                if elem:
                    text = elem.get_text(strip=True)
                    if text:
                        return text
            except:
                continue
        return None
    
    async def smart_crawl(self, keyword: str, location: str, max_pages: int) -> JobSearchResult:
        """智能爬取主函数"""
        Logger.info(f"🧠 开始智能爬取: {keyword} @ {location}, {max_pages}页")
        self.start_time = time.time()
        
        all_jobs = []
        successful_pages = 0
        
        # 城市代码映射
        city_codes = {
            "北京": "101010100",
            "上海": "101020100", 
            "广州": "101280100",
            "深圳": "101280600",
            "杭州": "101210100"
        }
        city_code = city_codes.get(location, "101010100")
        
        for page in range(1, max_pages + 1):
            encoded_keyword = quote(keyword)
            url = f"https://www.zhipin.com/web/geek/job?query={encoded_keyword}&city={city_code}&page={page}"
            
            jobs = await self.smart_crawl_page(url, page)
            if jobs:
                all_jobs.extend(jobs)
                successful_pages += 1
            
            # 检查是否需要停止
            if self.session_manager.session_data.get('failure_count', 0) >= 3:
                Logger.warning("⚠️ 连续失败次数过多，停止爬取")
                break
        
        # 去重
        unique_jobs = self._deduplicate_jobs(all_jobs)
        
        elapsed_time = time.time() - self.start_time
        Logger.info(f"📊 成功爬取 {successful_pages}/{max_pages} 页，获取 {len(all_jobs)} 个原始职位")
        Logger.success(f"✅ 去重后获得 {len(unique_jobs)} 个唯一职位")
        Logger.info(f"⏱️ 总执行时间: {elapsed_time:.2f}秒")
        
        return JobSearchResult(
            keyword=keyword,
            location=location,
            total_pages=max_pages,
            successful_pages=successful_pages,
            jobs=unique_jobs,
            crawl_time=datetime.now()
        )
    
    def _deduplicate_jobs(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """去重职位"""
        unique_jobs = []
        seen_keys = set()
        
        for job in jobs:
            key = f"{job.job_title}_{job.company_name}_{job.salary_range}"
            if key not in seen_keys:
                seen_keys.add(key)
                unique_jobs.append(job)
        
        return unique_jobs


# 便捷函数
async def smart_crawl_boss_jobs(
    keyword: str = "Python",
    location: str = "北京", 
    max_pages: int = 3,
    output_format: str = "json",
    output_file: str = "smart_boss_jobs.json"
) -> JobSearchResult:
    """智能爬取BOSS直聘职位"""
    config = CrawlerConfig(
        keyword=keyword,
        location=location,
        max_pages=max_pages,
        output_format=output_format,
        output_file=output_file,
        headless=True
    )
    
    async with SmartBossCrawler(config) as crawler:
        return await crawler.smart_crawl(keyword, location, max_pages)


if __name__ == "__main__":
    async def test_smart_crawler():
        result = await smart_crawl_boss_jobs("Python", "北京", 2)
        print(f"智能爬取完成，获取{len(result.jobs)}个职位")
    
    asyncio.run(test_smart_crawler())
