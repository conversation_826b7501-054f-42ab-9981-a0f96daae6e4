#!/usr/bin/env python3
"""
备用招聘网站爬虫 - 支持多个招聘平台
当BOSS直聘无法访问时的备用方案
"""

import asyncio
import json
import time
import random
from typing import List, Dict, Any, Optional
from urllib.parse import quote
from datetime import datetime

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from data_models import JobPosition, JobSearchResult, CrawlerConfig
from utils import Logger


class AlternativeCrawler:
    """备用招聘网站爬虫"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.crawler = None
        
    async def __aenter__(self):
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        
    async def initialize(self):
        """初始化爬虫"""
        Logger.info("🔄 启动备用爬虫...")
        
        browser_config = BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()
        
        Logger.success("✅ 备用爬虫初始化完成")
        
    async def cleanup(self):
        """清理资源"""
        if self.crawler:
            await self.crawler.__aexit__(None, None, None)
    
    async def crawl_lagou(self, keyword: str, location: str, max_pages: int) -> JobSearchResult:
        """爬取拉勾网"""
        Logger.info(f"🔄 开始爬取拉勾网: {keyword} @ {location}")
        
        all_jobs = []
        successful_pages = 0
        
        # 拉勾网城市代码映射
        city_codes = {
            "北京": "c2",
            "上海": "c3", 
            "广州": "c16",
            "深圳": "c4",
            "杭州": "c7"
        }
        city_code = city_codes.get(location, "c2")
        
        for page in range(1, min(max_pages + 1, 4)):  # 拉勾网限制页数
            try:
                await asyncio.sleep(3 + random.uniform(2, 5))
                
                url = f"https://www.lagou.com/wn/jobs?kd={quote(keyword)}&city={city_code}&pn={page}"
                
                run_config = CrawlerRunConfig(
                    page_timeout=30000,
                    delay_before_return_html=3.0,
                    wait_for_images=False
                )
                
                Logger.info(f"🔄 爬取拉勾网第{page}页")
                result = await self.crawler.arun(url, config=run_config)
                
                if result.success:
                    jobs = self._parse_lagou_jobs(result.html, page)
                    if jobs:
                        all_jobs.extend(jobs)
                        successful_pages += 1
                        Logger.success(f"✅ 拉勾网第{page}页完成，获取{len(jobs)}个职位")
                    else:
                        Logger.warning(f"⚠️ 拉勾网第{page}页无数据")
                else:
                    Logger.warning(f"⚠️ 拉勾网第{page}页请求失败")
                    
            except Exception as e:
                Logger.error(f"❌ 拉勾网第{page}页异常: {e}")
        
        return JobSearchResult(
            keyword=keyword,
            location=location,
            total_pages=max_pages,
            successful_pages=successful_pages,
            jobs=all_jobs,
            crawl_time=datetime.now()
        )
    
    def _parse_lagou_jobs(self, html: str, page_num: int) -> List[JobPosition]:
        """解析拉勾网职位数据"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            jobs = []
            
            # 拉勾网的选择器
            job_elements = soup.select('.item_con_list .con_list_item')
            
            for element in job_elements:
                try:
                    # 职位标题
                    title_elem = element.select_one('.list_item_top .position .p_top a')
                    if not title_elem:
                        continue
                    job_title = title_elem.get_text(strip=True)
                    
                    # 薪资
                    salary_elem = element.select_one('.list_item_top .position .p_bot .money')
                    salary = salary_elem.get_text(strip=True) if salary_elem else "面议"
                    
                    # 公司名称
                    company_elem = element.select_one('.list_item_top .company .company_name a')
                    company = company_elem.get_text(strip=True) if company_elem else "未知公司"
                    
                    # 地点
                    location_elem = element.select_one('.list_item_top .position .p_bot .el')
                    location = location_elem.get_text(strip=True) if location_elem else "北京"
                    
                    job = JobPosition(
                        job_title=job_title,
                        salary_range=salary,
                        company_name=company,
                        location=location,
                        work_experience="不限",
                        education="不限",
                        job_detail_url="",
                        company_scale="未知",
                        company_industry="未知",
                        job_description="",
                        hr_info=None,
                        publish_time=None,
                        crawl_time=datetime.now()
                    )
                    jobs.append(job)
                    
                except Exception as e:
                    continue
            
            return jobs
            
        except Exception as e:
            Logger.warning(f"⚠️ 拉勾网第{page_num}页解析失败: {e}")
            return []
    
    async def crawl_zhilian(self, keyword: str, location: str, max_pages: int) -> JobSearchResult:
        """爬取智联招聘"""
        Logger.info(f"🔄 开始爬取智联招聘: {keyword} @ {location}")
        
        all_jobs = []
        successful_pages = 0
        
        # 智联招聘城市代码映射
        city_codes = {
            "北京": "530",
            "上海": "538", 
            "广州": "763",
            "深圳": "765",
            "杭州": "653"
        }
        city_code = city_codes.get(location, "530")
        
        for page in range(1, max_pages + 1):
            try:
                await asyncio.sleep(4 + random.uniform(2, 6))
                
                url = f"https://sou.zhaopin.com/jobs/searchresult.ashx?jl={city_code}&kw={quote(keyword)}&p={page}"
                
                run_config = CrawlerRunConfig(
                    page_timeout=30000,
                    delay_before_return_html=4.0,
                    wait_for_images=False
                )
                
                Logger.info(f"🔄 爬取智联招聘第{page}页")
                result = await self.crawler.arun(url, config=run_config)
                
                if result.success:
                    jobs = self._parse_zhilian_jobs(result.html, page)
                    if jobs:
                        all_jobs.extend(jobs)
                        successful_pages += 1
                        Logger.success(f"✅ 智联招聘第{page}页完成，获取{len(jobs)}个职位")
                    else:
                        Logger.warning(f"⚠️ 智联招聘第{page}页无数据")
                else:
                    Logger.warning(f"⚠️ 智联招聘第{page}页请求失败")
                    
            except Exception as e:
                Logger.error(f"❌ 智联招聘第{page}页异常: {e}")
        
        return JobSearchResult(
            keyword=keyword,
            location=location,
            total_pages=max_pages,
            successful_pages=successful_pages,
            jobs=all_jobs,
            crawl_time=datetime.now()
        )
    
    def _parse_zhilian_jobs(self, html: str, page_num: int) -> List[JobPosition]:
        """解析智联招聘职位数据"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            jobs = []
            
            # 智联招聘的选择器
            job_elements = soup.select('.newlist_list_content table tr')
            
            for element in job_elements:
                try:
                    # 跳过表头
                    if element.select_one('th'):
                        continue
                    
                    # 职位标题
                    title_elem = element.select_one('td.zwmc a')
                    if not title_elem:
                        continue
                    job_title = title_elem.get_text(strip=True)
                    
                    # 薪资
                    salary_elem = element.select_one('td.zwyx')
                    salary = salary_elem.get_text(strip=True) if salary_elem else "面议"
                    
                    # 公司名称
                    company_elem = element.select_one('td.gsmc a')
                    company = company_elem.get_text(strip=True) if company_elem else "未知公司"
                    
                    # 地点
                    location_elem = element.select_one('td.gzdd')
                    location = location_elem.get_text(strip=True) if location_elem else "北京"
                    
                    job = JobPosition(
                        job_title=job_title,
                        salary_range=salary,
                        company_name=company,
                        location=location,
                        work_experience="不限",
                        education="不限",
                        job_detail_url="",
                        company_scale="未知",
                        company_industry="未知",
                        job_description="",
                        hr_info=None,
                        publish_time=None,
                        crawl_time=datetime.now()
                    )
                    jobs.append(job)
                    
                except Exception as e:
                    continue
            
            return jobs
            
        except Exception as e:
            Logger.warning(f"⚠️ 智联招聘第{page_num}页解析失败: {e}")
            return []
    
    async def crawl_multiple_sites(self, keyword: str, location: str, max_pages: int) -> JobSearchResult:
        """爬取多个招聘网站"""
        Logger.info(f"🔄 开始爬取多个招聘网站: {keyword} @ {location}")
        
        all_jobs = []
        total_successful_pages = 0
        
        # 爬取拉勾网
        try:
            Logger.info("📋 开始爬取拉勾网...")
            lagou_result = await self.crawl_lagou(keyword, location, max_pages)
            all_jobs.extend(lagou_result.jobs)
            total_successful_pages += lagou_result.successful_pages
            Logger.info(f"✅ 拉勾网完成，获取{len(lagou_result.jobs)}个职位")
        except Exception as e:
            Logger.error(f"❌ 拉勾网爬取失败: {e}")
        
        # 等待一段时间
        await asyncio.sleep(10)
        
        # 爬取智联招聘
        try:
            Logger.info("📋 开始爬取智联招聘...")
            zhilian_result = await self.crawl_zhilian(keyword, location, max_pages)
            all_jobs.extend(zhilian_result.jobs)
            total_successful_pages += zhilian_result.successful_pages
            Logger.info(f"✅ 智联招聘完成，获取{len(zhilian_result.jobs)}个职位")
        except Exception as e:
            Logger.error(f"❌ 智联招聘爬取失败: {e}")
        
        # 去重
        unique_jobs = self._deduplicate_jobs(all_jobs)
        
        Logger.info(f"📊 多站点爬取完成，总共获取 {len(all_jobs)} 个原始职位")
        Logger.success(f"✅ 去重后获得 {len(unique_jobs)} 个唯一职位")
        
        return JobSearchResult(
            keyword=keyword,
            location=location,
            total_pages=max_pages * 2,  # 两个网站
            successful_pages=total_successful_pages,
            jobs=unique_jobs,
            crawl_time=datetime.now()
        )
    
    def _deduplicate_jobs(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """去重职位"""
        unique_jobs = []
        seen_keys = set()
        
        for job in jobs:
            key = f"{job.job_title}_{job.company_name}"
            if key not in seen_keys:
                seen_keys.add(key)
                unique_jobs.append(job)
        
        return unique_jobs


# 便捷函数
async def crawl_alternative_sites(
    keyword: str = "Python",
    location: str = "北京", 
    max_pages: int = 3,
    output_format: str = "json",
    output_file: str = "alternative_jobs.json"
) -> JobSearchResult:
    """爬取备用招聘网站"""
    config = CrawlerConfig(
        keyword=keyword,
        location=location,
        max_pages=max_pages,
        output_format=output_format,
        output_file=output_file,
        headless=True
    )
    
    async with AlternativeCrawler(config) as crawler:
        return await crawler.crawl_multiple_sites(keyword, location, max_pages)


if __name__ == "__main__":
    async def test_alternative_crawler():
        result = await crawl_alternative_sites("Python", "北京", 2)
        print(f"备用爬虫完成，获取{len(result.jobs)}个职位")
        
        # 保存结果
        with open('alternative_jobs.json', 'w', encoding='utf-8') as f:
            jobs_data = [job.__dict__ for job in result.jobs]
            json.dump(jobs_data, f, ensure_ascii=False, indent=2, default=str)
        print("结果已保存到 alternative_jobs.json")
    
    asyncio.run(test_alternative_crawler())
