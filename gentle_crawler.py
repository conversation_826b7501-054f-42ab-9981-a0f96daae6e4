#!/usr/bin/env python3
"""
温和的BOSS直聘爬虫 - 降低被检测的风险
"""

import asyncio
import json
import time
import random
from typing import List, Dict, Any
from urllib.parse import quote
import aiohttp
from datetime import datetime

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

from data_models import JobPosition, JobSearchResult, CrawlerConfig
from utils import Logger, DataExporter


class GentleBossCrawler:
    """温和的BOSS直聘爬虫 - 模拟真实用户行为"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.crawler = None
        self.session = None
        
    async def __aenter__(self):
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        
    async def initialize(self):
        """初始化爬虫"""
        Logger.info("🐌 启动温和爬虫...")
        
        # 更真实的浏览器配置
        browser_config = BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=1366,  # 更常见的分辨率
            viewport_height=768,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "--disable-features=VizDisplayCompositor",
            ]
        )
        
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()
        
        # 创建HTTP会话
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        )
        
        Logger.success("✅ 温和爬虫初始化完成")
        
    async def cleanup(self):
        """清理资源"""
        if self.crawler:
            await self.crawler.__aexit__(None, None, None)
        if self.session:
            await self.session.close()
            
    def get_gentle_stealth_js(self) -> str:
        """温和的隐身JavaScript"""
        return """
        // 基础反检测
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
        Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
        Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en']});
        
        // 模拟真实用户行为
        function simulateHumanBehavior() {
            // 随机滚动
            const scrollHeight = document.body.scrollHeight;
            const viewportHeight = window.innerHeight;
            const scrollSteps = 3 + Math.floor(Math.random() * 3);
            
            for (let i = 0; i < scrollSteps; i++) {
                setTimeout(() => {
                    const scrollTo = (scrollHeight / scrollSteps) * (i + 1);
                    window.scrollTo({
                        top: scrollTo,
                        behavior: 'smooth'
                    });
                }, 500 + Math.random() * 1000);
            }
            
            // 模拟鼠标移动
            setTimeout(() => {
                const event = new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight
                });
                document.dispatchEvent(event);
            }, 1000 + Math.random() * 2000);
        }
        
        // 等待页面加载完成后执行
        if (document.readyState === 'complete') {
            setTimeout(simulateHumanBehavior, 1000 + Math.random() * 2000);
        } else {
            window.addEventListener('load', () => {
                setTimeout(simulateHumanBehavior, 1000 + Math.random() * 2000);
            });
        }
        """
        
    async def gentle_crawl_page(self, url: str, page_num: int) -> List[JobPosition]:
        """温和地爬取单页"""
        try:
            # 随机延时
            delay = 3 + random.uniform(2, 5)
            Logger.info(f"🐌 等待 {delay:.1f}秒 后爬取第{page_num}页")
            await asyncio.sleep(delay)
            
            # 配置温和的运行参数
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,  # 30秒超时
                delay_before_return_html=5.0,  # 等待5秒
                js_code=[self.get_gentle_stealth_js()],
                wait_for_images=False,
                screenshot=False,
                simulate_user=True,  # 启用用户模拟
                override_navigator=True  # 覆盖导航器
            )
            
            Logger.info(f"🐌 温和爬取第{page_num}页")
            result = await self.crawler.arun(url=url, config=run_config)
            
            if result.success:
                # 检查是否遇到验证页面
                if "验证" in result.html or "captcha" in result.html.lower():
                    Logger.warning(f"⚠️ 第{page_num}页遇到验证，跳过")
                    return []
                
                # 简单的HTML解析（不使用复杂的提取策略）
                jobs = await self._simple_parse_jobs(result.html, page_num)
                Logger.success(f"✅ 第{page_num}页完成，获取{len(jobs)}个职位")
                return jobs
            else:
                Logger.warning(f"⚠️ 第{page_num}页爬取失败")
                return []
                
        except Exception as e:
            Logger.error(f"❌ 第{page_num}页爬取异常: {e}")
            return []
            
    async def _simple_parse_jobs(self, html: str, page_num: int) -> List[JobPosition]:
        """简单的HTML解析"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            jobs = []
            
            # 尝试多种可能的选择器
            job_selectors = [
                '.job-card-wrapper',
                '.job-card-left', 
                'li[ka*="search_list"]',
                '.job-list-item',
                '.job-card-body'
            ]
            
            job_elements = []
            for selector in job_selectors:
                elements = soup.select(selector)
                if elements:
                    job_elements = elements
                    break
            
            for element in job_elements[:15]:  # 限制每页最多15个
                try:
                    job = self._extract_job_from_element(element)
                    if job:
                        jobs.append(job)
                except Exception as e:
                    continue
                    
            return jobs
            
        except Exception as e:
            Logger.warning(f"⚠️ 第{page_num}页HTML解析失败: {e}")
            return []
            
    def _extract_job_from_element(self, element) -> JobPosition:
        """从HTML元素提取职位信息"""
        try:
            # 提取职位标题
            title_selectors = ['.job-name a', '.job-title', '.job-name', 'h3 a']
            job_title = None
            for selector in title_selectors:
                title_elem = element.select_one(selector)
                if title_elem:
                    job_title = title_elem.get_text(strip=True)
                    break
            
            if not job_title:
                return None
                
            # 提取其他信息
            salary_elem = element.select_one('.salary, .red')
            salary = salary_elem.get_text(strip=True) if salary_elem else "面议"
            
            company_elem = element.select_one('.company-name a, .company-name')
            company = company_elem.get_text(strip=True) if company_elem else "未知公司"
            
            # 创建职位对象
            return JobPosition(
                job_title=job_title,
                salary_range=salary,
                company_name=company,
                location="北京",  # 默认值
                work_experience="不限",
                education="不限",
                job_detail_url="",
                company_scale="未知",
                company_industry="未知",
                job_description="",
                hr_info=None,
                publish_time=None,
                crawl_time=datetime.now()
            )
            
        except Exception as e:
            return None
            
    async def gentle_crawl(self, keyword: str, location: str, max_pages: int) -> JobSearchResult:
        """温和的爬取方法"""
        Logger.info(f"🐌 开始温和爬取: {keyword} @ {location}, {max_pages}页")
        
        all_jobs = []
        successful_pages = 0
        
        # 获取城市代码
        city_code = "*********"  # 北京的代码
        
        for page in range(1, max_pages + 1):
            encoded_keyword = quote(keyword)
            url = f"https://www.zhipin.com/web/geek/job?query={encoded_keyword}&city={city_code}&page={page}"
            
            jobs = await self.gentle_crawl_page(url, page)
            if jobs:
                all_jobs.extend(jobs)
                successful_pages += 1
            
            # 页面间随机延时
            if page < max_pages:
                delay = 5 + random.uniform(3, 8)
                Logger.info(f"⏸️ 页面间等待 {delay:.1f}秒")
                await asyncio.sleep(delay)
        
        # 去重
        unique_jobs = []
        seen_titles = set()
        for job in all_jobs:
            key = f"{job.job_title}_{job.company_name}"
            if key not in seen_titles:
                seen_titles.add(key)
                unique_jobs.append(job)
        
        Logger.info(f"📊 成功爬取 {successful_pages}/{max_pages} 页，获取 {len(all_jobs)} 个原始职位")
        Logger.success(f"✅ 去重后获得 {len(unique_jobs)} 个唯一职位")
        
        return JobSearchResult(
            keyword=keyword,
            location=location,
            total_pages=max_pages,
            successful_pages=successful_pages,
            jobs=unique_jobs,
            crawl_time=datetime.now()
        )


# 便捷函数
async def gentle_crawl_boss_jobs(
    keyword: str = "Python",
    location: str = "北京", 
    max_pages: int = 3,  # 减少页数
    output_format: str = "json",
    output_file: str = "gentle_boss_jobs.json"
) -> JobSearchResult:
    """温和爬取BOSS直聘职位"""
    config = CrawlerConfig(
        keyword=keyword,
        location=location,
        max_pages=max_pages,
        output_format=output_format,
        output_file=output_file,
        headless=True
    )
    
    async with GentleBossCrawler(config) as crawler:
        return await crawler.gentle_crawl(keyword, location, max_pages)


if __name__ == "__main__":
    async def test_gentle_crawler():
        result = await gentle_crawl_boss_jobs("Python", "北京", 2)
        print(f"温和爬取完成，获取{len(result.jobs)}个职位")
    
    asyncio.run(test_gentle_crawler())
