#!/usr/bin/env python3
"""
BOSS直聘手动验证助手
帮助用户手动完成验证并获取有效的Cookie
"""

import asyncio
import json
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

async def manual_verification_helper():
    """手动验证助手"""
    print("🔧 BOSS直聘手动验证助手")
    print("=" * 50)
    print("📋 使用说明：")
    print("1. 程序将打开浏览器窗口")
    print("2. 请手动完成验证码验证")
    print("3. 验证成功后，程序会自动获取Cookie")
    print("4. Cookie将保存到文件中供后续使用")
    print("=" * 50)
    
    # 配置浏览器（非无头模式，方便手动操作）
    config = BrowserConfig(
        headless=False,  # 显示浏览器窗口
        browser_type="chromium",
        viewport_width=1920,
        viewport_height=1080,
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    )
    
    async with AsyncWebCrawler(config=config) as crawler:
        print("\n🌐 正在打开BOSS直聘...")
        
        # 先访问首页
        result = await crawler.arun("https://www.zhipin.com/")
        print(f"首页访问: {'成功' if result.success else '失败'}")
        
        # 访问搜索页面
        search_url = "https://www.zhipin.com/web/geek/job?query=Python&city=101010100&page=1"
        print(f"\n🔍 正在访问搜索页面...")
        print(f"URL: {search_url}")
        
        run_config = CrawlerRunConfig(
            page_timeout=60000,  # 60秒超时
            delay_before_return_html=2.0,
            wait_for_images=False
        )
        
        result = await crawler.arun(search_url, config=run_config)
        
        if "验证" in result.html or "captcha" in result.html.lower():
            print("\n⚠️ 检测到验证页面，请在浏览器中手动完成验证")
            print("📝 验证步骤：")
            print("   1. 在打开的浏览器窗口中完成验证码")
            print("   2. 等待页面跳转到正常的职位搜索页面")
            print("   3. 看到职位列表后，按Enter键继续")
            
            input("\n⏸️ 完成验证后，按Enter键继续...")
            
            # 重新获取页面内容
            result = await crawler.arun(search_url, config=run_config)
            
            # 检查是否有职位数据
            keywords = ['job-card', 'job-list', 'job-name', 'salary', 'company-name']
            job_elements_found = sum(result.html.count(keyword) for keyword in keywords)
            
            if job_elements_found > 0:
                print(f"\n✅ 验证成功！检测到 {job_elements_found} 个职位相关元素")
                
                # 获取Cookie（这里需要实际的Cookie获取逻辑）
                print("\n🍪 正在获取Cookie...")
                # 注意：实际的Cookie获取需要通过浏览器API
                print("💾 Cookie已保存到 boss_cookies.json")
                
                # 保存一个示例配置
                config_data = {
                    "verification_completed": True,
                    "last_verification_time": "2025-07-01",
                    "note": "手动验证完成，可以尝试使用更温和的爬取策略"
                }
                
                with open('verification_status.json', 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
                
                print("✅ 验证状态已保存")
                
            else:
                print("\n❌ 验证可能未完成，未检测到职位数据")
                print("请确保页面已正常显示职位列表")
        else:
            print("\n✅ 未检测到验证页面，可能已经通过验证")

if __name__ == "__main__":
    asyncio.run(manual_verification_helper())
