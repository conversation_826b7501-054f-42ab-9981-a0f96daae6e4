#!/usr/bin/env python3
"""
BOSS直聘爬虫调试工具
"""

import asyncio
import json
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

async def test_boss_access():
    """测试BOSS直聘访问"""
    print("🔍 开始诊断BOSS直聘访问...")
    
    # 测试1: 基本访问
    print("\n📋 测试1: 基本页面访问")
    config = BrowserConfig(
        headless=False,  # 显示浏览器窗口
        browser_type="chromium",
        viewport_width=1920,
        viewport_height=1080,
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    )
    
    async with AsyncWebCrawler(config=config) as crawler:
        # 先访问首页
        print("访问BOSS直聘首页...")
        result = await crawler.arun("https://www.zhipin.com/")
        print(f"首页访问: {'成功' if result.success else '失败'}")
        print(f"状态码: {result.status_code}")
        print(f"HTML长度: {len(result.html) if result.html else 0}")
        
        # 等待用户观察
        await asyncio.sleep(3)
        
        # 访问搜索页面
        print("\n访问搜索页面...")
        search_url = "https://www.zhipin.com/web/geek/job?query=Python&city=101010100&page=1"
        
        run_config = CrawlerRunConfig(
            page_timeout=20000,
            delay_before_return_html=5.0,
            wait_for_images=False,
            js_code=["""
                // 基本反检测
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                
                // 等待页面加载
                console.log('等待页面加载...');
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // 检查页面内容
                const jobCards = document.querySelectorAll('[class*="job"]');
                console.log('找到的job相关元素:', jobCards.length);
                
                // 滚动页面
                window.scrollTo(0, document.body.scrollHeight / 2);
                await new Promise(resolve => setTimeout(resolve, 2000));
            """]
        )
        
        result = await crawler.arun(search_url, config=run_config)
        print(f"搜索页访问: {'成功' if result.success else '失败'}")
        print(f"状态码: {result.status_code}")
        print(f"HTML长度: {len(result.html) if result.html else 0}")
        
        if result.html:
            # 检查关键元素
            keywords = ['job-card', 'job-list', 'job-name', 'salary', 'company-name', 'Python', '职位', '薪资']
            print("\n🔍 关键词检查:")
            for keyword in keywords:
                count = result.html.count(keyword)
                print(f"  {keyword}: {count} 次")
            
            # 保存HTML用于分析
            with open('debug_page.html', 'w', encoding='utf-8') as f:
                f.write(result.html)
            print("\n💾 页面HTML已保存到 debug_page.html")
            
            # 检查是否有验证码或登录要求
            if '验证' in result.html or 'login' in result.html.lower() or '登录' in result.html:
                print("⚠️ 检测到可能需要验证或登录")
            
            # 检查是否被重定向
            if 'redirect' in result.html.lower() or result.status_code in [301, 302, 303, 307, 308]:
                print("⚠️ 检测到页面重定向")
        
        # 等待用户观察浏览器窗口
        print("\n⏸️ 请观察浏览器窗口，按Enter继续...")
        input()

if __name__ == "__main__":
    asyncio.run(test_boss_access())
