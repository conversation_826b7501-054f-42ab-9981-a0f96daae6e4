"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[250],{19250:function(e,t,o){o.d(t,{$D:function(){return eJ},$I:function(){return Q},$o:function(){return tF},AZ:function(){return Y},Au:function(){return e_},BL:function(){return eZ},Br:function(){return b},Bw:function(){return tx},E9:function(){return eY},EB:function(){return th},EG:function(){return eW},EY:function(){return e0},Eb:function(){return N},FC:function(){return eh},Gh:function(){return eG},H1:function(){return A},H2:function(){return r},Hx:function(){return ej},I1:function(){return S},It:function(){return O},J$:function(){return ec},JO:function(){return B},K8:function(){return h},K_:function(){return eQ},Ko:function(){return tN},LY:function(){return eV},Lp:function(){return eR},MO:function(){return p},Mx:function(){return tf},N3:function(){return eb},N8:function(){return ea},NL:function(){return e9},NV:function(){return m},Nc:function(){return eP},Nz:function(){return e1},O3:function(){return eD},OD:function(){return eC},OU:function(){return eu},Of:function(){return F},Og:function(){return y},Ou:function(){return ty},Ov:function(){return C},Oz:function(){return tO},PC:function(){return e6},PT:function(){return K},PY:function(){return tv},Pj:function(){return tt},Pv:function(){return tm},Qg:function(){return ex},RQ:function(){return j},Rg:function(){return et},Sb:function(){return ez},So:function(){return er},TF:function(){return tu},Tj:function(){return e2},Tx:function(){return tB},U8:function(){return te},UM:function(){return tl},VA:function(){return I},Vt:function(){return eX},W_:function(){return M},X:function(){return es},XB:function(){return tg},XO:function(){return T},Xd:function(){return eS},Xm:function(){return x},YU:function(){return eH},Yi:function(){return tj},Yo:function(){return U},Z9:function(){return V},Zr:function(){return k},a6:function(){return P},aC:function(){return tw},ao:function(){return eK},b1:function(){return ew},cq:function(){return R},cu:function(){return eA},e2:function(){return eT},eH:function(){return W},eW:function(){return tC},eZ:function(){return eO},fE:function(){return td},fP:function(){return eo},fk:function(){return tS},g:function(){return e4},gX:function(){return eB},gl:function(){return to},h3:function(){return ed},hT:function(){return ev},hy:function(){return f},ix:function(){return X},j2:function(){return ei},jA:function(){return e$},jE:function(){return eM},jr:function(){return tT},kK:function(){return g},kn:function(){return $},lP:function(){return w},lU:function(){return tr},lg:function(){return eN},mC:function(){return ti},mR:function(){return en},mY:function(){return tc},m_:function(){return D},mp:function(){return eq},n$:function(){return em},n9:function(){return ts},nJ:function(){return tb},nd:function(){return e7},o6:function(){return ee},oC:function(){return eF},ol:function(){return L},pf:function(){return eL},pu:function(){return tk},qI:function(){return _},qW:function(){return t_},qd:function(){return tE},qk:function(){return e8},qm:function(){return u},r1:function(){return tp},r6:function(){return G},rs:function(){return v},s0:function(){return Z},sN:function(){return eI},t$:function(){return J},t0:function(){return eE},t3:function(){return e5},tB:function(){return tn},tN:function(){return ep},u5:function(){return el},v9:function(){return ek},vh:function(){return eU},wX:function(){return E},wd:function(){return eg},xA:function(){return ey},xO:function(){return e3},xX:function(){return z},xZ:function(){return ta},zX:function(){return c},zg:function(){return ef}});var a=o(41021);let r=null;console.log=function(){};let n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=window.location.origin,a=t||o;console.log("proxyBaseUrl:",r),console.log("serverRootPath:",e),e.length>0&&!a.endsWith(e)&&"/"!=e&&(a+=e,r=a),console.log("Updated proxyBaseUrl:",r)},c=()=>r||window.location.origin,s={GET:"GET",DELETE:"DELETE"},i=0,l=async e=>{let t=Date.now();t-i>6e4?(e.includes("Authentication Error - Expired Key")&&(a.ZP.info("UI Session Expired. Logging out."),i=t,document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",window.location.href=window.location.pathname),i=t):console.log("Error suppressed to prevent spam:",e)},d="Authorization";function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Authorization";console.log("setGlobalLitellmHeaderName: ".concat(e)),d=e}let p=async()=>{console.log("Getting UI config");let e=await fetch("/litellm/.well-known/litellm-ui-config"),t=await e.json();return console.log("jsonData in getUiConfig:",t),n(t.server_root_path,t.proxy_base_url),t},w=async()=>{let e=r?"".concat(r,"/openapi.json"):"/openapi.json",t=await fetch(e);return await t.json()},u=async e=>{try{let t=r?"".concat(r,"/get/litellm_model_cost_map"):"/get/litellm_model_cost_map",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}}),a=await o.json();return console.log("received litellm model cost data: ".concat(a)),a}catch(e){throw console.error("Failed to get model cost map:",e),e}},g=async(e,t)=>{try{let o=r?"".concat(r,"/model/new"):"/model/new",n=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!n.ok){let e=await n.text()||"Network response was not ok";throw a.ZP.error(e),Error(e)}let c=await n.json();return console.log("API Response:",c),a.ZP.destroy(),a.ZP.success("Model ".concat(t.model_name," created successfully"),2),c}catch(e){throw console.error("Failed to create key:",e),e}},f=async e=>{try{let t=r?"".concat(r,"/model/settings"):"/model/settings",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){console.error("Failed to get model settings:",e)}},y=async(e,t)=>{console.log("model_id in model delete call: ".concat(t));try{let o=r?"".concat(r,"/model/delete"):"/model/delete",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({id:t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},m=async(e,t)=>{if(console.log("budget_id in budget delete call: ".concat(t)),null!=e)try{let o=r?"".concat(r,"/budget/delete"):"/budget/delete",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({id:t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},k=async(e,t)=>{try{console.log("Form Values in budgetCreateCall:",t),console.log("Form Values after check:",t);let o=r?"".concat(r,"/budget/new"):"/budget/new",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},_=async(e,t)=>{try{console.log("Form Values in budgetUpdateCall:",t),console.log("Form Values after check:",t);let o=r?"".concat(r,"/budget/update"):"/budget/update",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},T=async(e,t)=>{try{let o=r?"".concat(r,"/invitation/new"):"/invitation/new",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},j=async e=>{try{let t=r?"".concat(r,"/alerting/settings"):"/alerting/settings",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},E=async(e,t,o)=>{try{if(console.log("Form Values in keyCreateCall:",o),o.description&&(o.metadata||(o.metadata={}),o.metadata.description=o.description,delete o.description,o.metadata=JSON.stringify(o.metadata)),o.metadata){console.log("formValues.metadata:",o.metadata);try{o.metadata=JSON.parse(o.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}console.log("Form Values after check:",o);let a=r?"".concat(r,"/key/generate"):"/key/generate",n=await fetch(a,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t,...o})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error response from the server:",e),Error(e)}let c=await n.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to create key:",e),e}},C=async(e,t,o)=>{try{if(console.log("Form Values in keyCreateCall:",o),o.description&&(o.metadata||(o.metadata={}),o.metadata.description=o.description,delete o.description,o.metadata=JSON.stringify(o.metadata)),o.auto_create_key=!1,o.metadata){console.log("formValues.metadata:",o.metadata);try{o.metadata=JSON.parse(o.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}console.log("Form Values after check:",o);let a=r?"".concat(r,"/user/new"):"/user/new",n=await fetch(a,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t,...o})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error response from the server:",e),Error(e)}let c=await n.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to create key:",e),e}},S=async(e,t)=>{try{let o=r?"".concat(r,"/key/delete"):"/key/delete";console.log("in keyDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({keys:[t]})});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log(n),n}catch(e){throw console.error("Failed to create key:",e),e}},N=async(e,t)=>{try{let o=r?"".concat(r,"/user/delete"):"/user/delete";console.log("in userDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_ids:t})});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log(n),n}catch(e){throw console.error("Failed to delete user(s):",e),e}},v=async(e,t)=>{try{let o=r?"".concat(r,"/team/delete"):"/team/delete";console.log("in teamDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_ids:[t]})});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log(n),n}catch(e){throw console.error("Failed to delete key:",e),e}},F=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,c=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:null,i=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,h=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null,p=arguments.length>9&&void 0!==arguments[9]?arguments[9]:null;try{let w=r?"".concat(r,"/user/list"):"/user/list";console.log("in userListCall");let u=new URLSearchParams;if(t&&t.length>0){let e=t.join(",");u.append("user_ids",e)}o&&u.append("page",o.toString()),a&&u.append("page_size",a.toString()),n&&u.append("user_email",n),c&&u.append("role",c),s&&u.append("team",s),i&&u.append("sso_user_ids",i),h&&u.append("sort_by",h),p&&u.append("sort_order",p);let g=u.toString();g&&(w+="?".concat(g));let f=await fetch(w,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!f.ok){let e=await f.text();throw l(e),Error("Network response was not ok")}let y=await f.json();return console.log("/user/list API Response:",y),y}catch(e){throw console.error("Failed to create key:",e),e}},b=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=arguments.length>4?arguments[4]:void 0,c=arguments.length>5?arguments[5]:void 0,s=arguments.length>6&&void 0!==arguments[6]&&arguments[6];console.log("userInfoCall: ".concat(t,", ").concat(o,", ").concat(a,", ").concat(n,", ").concat(c,", ").concat(s));try{let i;if(a){i=r?"".concat(r,"/user/list"):"/user/list";let e=new URLSearchParams;null!=n&&e.append("page",n.toString()),null!=c&&e.append("page_size",c.toString()),i+="?".concat(e.toString())}else i=r?"".concat(r,"/user/info"):"/user/info",("Admin"!==o&&"Admin Viewer"!==o||s)&&t&&(i+="?user_id=".concat(t));console.log("Requesting user data from:",i);let h=await fetch(i,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!h.ok){let e=await h.text();throw l(e),Error("Network response was not ok")}let p=await h.json();return console.log("API Response:",p),p}catch(e){throw console.error("Failed to fetch user data:",e),e}},x=async(e,t)=>{try{let o=r?"".concat(r,"/team/info"):"/team/info";t&&(o="".concat(o,"?team_id=").concat(t)),console.log("in teamInfoCall");let a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},B=async function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;arguments.length>5&&void 0!==arguments[5]&&arguments[5],arguments.length>6&&void 0!==arguments[6]&&arguments[6],arguments.length>7&&void 0!==arguments[7]&&arguments[7],arguments.length>8&&void 0!==arguments[8]&&arguments[8];try{let c=r?"".concat(r,"/v2/team/list"):"/v2/team/list";console.log("in teamInfoCall");let s=new URLSearchParams;o&&s.append("user_id",o.toString()),t&&s.append("organization_id",t.toString()),a&&s.append("team_id",a.toString()),n&&s.append("team_alias",n.toString());let i=s.toString();i&&(c+="?".concat(i));let h=await fetch(c,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!h.ok){let e=await h.text();throw l(e),Error("Network response was not ok")}let p=await h.json();return console.log("/v2/team/list API Response:",p),p}catch(e){throw console.error("Failed to create key:",e),e}},O=async function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let c=r?"".concat(r,"/team/list"):"/team/list";console.log("in teamInfoCall");let s=new URLSearchParams;o&&s.append("user_id",o.toString()),t&&s.append("organization_id",t.toString()),a&&s.append("team_id",a.toString()),n&&s.append("team_alias",n.toString());let i=s.toString();i&&(c+="?".concat(i));let h=await fetch(c,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!h.ok){let e=await h.text();throw l(e),Error("Network response was not ok")}let p=await h.json();return console.log("/team/list API Response:",p),p}catch(e){throw console.error("Failed to create key:",e),e}},P=async e=>{try{let t=r?"".concat(r,"/team/available"):"/team/available";console.log("in availableTeamListCall");let o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log("/team/available_teams API Response:",a),a}catch(e){throw e}},G=async e=>{try{let t=r?"".concat(r,"/organization/list"):"/organization/list",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},J=async(e,t)=>{try{let o=r?"".concat(r,"/organization/info"):"/organization/info";t&&(o="".concat(o,"?organization_id=").concat(t)),console.log("in teamInfoCall");let a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},A=async(e,t)=>{try{if(console.log("Form Values in organizationCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw console.error("Failed to parse metadata:",e),Error("Failed to parse metadata: "+e)}}let o=r?"".concat(r,"/organization/new"):"/organization/new",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},I=async(e,t)=>{try{console.log("Form Values in organizationUpdateCall:",t);let o=r?"".concat(r,"/organization/update"):"/organization/update",a=await fetch(o,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("Update Team Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},R=async(e,t)=>{try{let o=r?"".concat(r,"/organization/delete"):"/organization/delete",a=await fetch(o,{method:"DELETE",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_ids:[t]})});if(!a.ok){let e=await a.text();throw l(e),Error("Error deleting organization: ".concat(e))}return await a.json()}catch(e){throw console.error("Failed to delete organization:",e),e}},U=async(e,t)=>{try{let o=r?"".concat(r,"/utils/transform_request"):"/utils/transform_request",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to create key:",e),e}},z=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;try{let n=r?"".concat(r,"/user/daily/activity"):"/user/daily/activity",c=new URLSearchParams,s=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};c.append("start_date",s(t)),c.append("end_date",s(o)),c.append("page_size","1000"),c.append("page",a.toString());let i=c.toString();i&&(n+="?".concat(i));let h=await fetch(n,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!h.ok){let e=await h.text();throw l(e),Error("Network response was not ok")}return await h.json()}catch(e){throw console.error("Failed to create key:",e),e}},V=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let c=r?"".concat(r,"/tag/daily/activity"):"/tag/daily/activity",s=new URLSearchParams,i=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};s.append("start_date",i(t)),s.append("end_date",i(o)),s.append("page_size","1000"),s.append("page",a.toString()),n&&s.append("tags",n.join(","));let h=s.toString();h&&(c+="?".concat(h));let p=await fetch(c,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!p.ok){let e=await p.text();throw l(e),Error("Network response was not ok")}return await p.json()}catch(e){throw console.error("Failed to create key:",e),e}},L=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let c=r?"".concat(r,"/team/daily/activity"):"/team/daily/activity",s=new URLSearchParams,i=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};s.append("start_date",i(t)),s.append("end_date",i(o)),s.append("page_size","1000"),s.append("page",a.toString()),n&&s.append("team_ids",n.join(",")),s.append("exclude_team_ids","litellm-dashboard");let h=s.toString();h&&(c+="?".concat(h));let p=await fetch(c,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!p.ok){let e=await p.text();throw l(e),Error("Network response was not ok")}return await p.json()}catch(e){throw console.error("Failed to create key:",e),e}},M=async e=>{try{let t=r?"".concat(r,"/onboarding/get_token"):"/onboarding/get_token";t+="?invite_link=".concat(e);let o=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},D=async(e,t,o,a)=>{let n=r?"".concat(r,"/onboarding/claim_token"):"/onboarding/claim_token";try{let r=await fetch(n,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({invitation_link:t,user_id:o,password:a})});if(!r.ok){let e=await r.text();throw l(e),Error("Network response was not ok")}let c=await r.json();return console.log(c),c}catch(e){throw console.error("Failed to delete key:",e),e}},Z=async(e,t,o)=>{try{let a=r?"".concat(r,"/key/").concat(t,"/regenerate"):"/key/".concat(t,"/regenerate"),n=await fetch(a,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok){let e=await n.text();throw l(e),Error("Network response was not ok")}let c=await n.json();return console.log("Regenerate key Response:",c),c}catch(e){throw console.error("Failed to regenerate key:",e),e}},H=!1,q=null,Y=async(e,t,o)=>{try{console.log("modelInfoCall:",e,t,o);let n=r?"".concat(r,"/v2/model/info"):"/v2/model/info",c=new URLSearchParams;c.append("include_team_models","true"),c.toString()&&(n+="?".concat(c.toString()));let s=await fetch(n,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok){let e=await s.text();throw e+="error shown=".concat(H),H||(e.includes("No model list passed")&&(e="No Models Exist. Click Add Model to get started."),a.ZP.info(e,10),H=!0,q&&clearTimeout(q),q=setTimeout(()=>{H=!1},1e4)),Error("Network response was not ok")}let i=await s.json();return console.log("modelInfoCall:",i),i}catch(e){throw console.error("Failed to create key:",e),e}},X=async(e,t)=>{try{let o=r?"".concat(r,"/v1/model/info"):"/v1/model/info";o+="?litellm_model_id=".concat(t);let a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok)throw await a.text(),Error("Network response was not ok");let n=await a.json();return console.log("modelInfoV1Call:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},$=async e=>{try{let t=r?"".concat(r,"/model_group/info"):"/model_group/info",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");let a=await o.json();return console.log("modelHubCall:",a),a}catch(e){throw console.error("Failed to create key:",e),e}},K=async e=>{try{let t=r?"".concat(r,"/get/allowed_ips"):"/get/allowed_ips",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw Error("Network response was not ok: ".concat(e))}let a=await o.json();return console.log("getAllowedIPs:",a),a.data}catch(e){throw console.error("Failed to get allowed IPs:",e),e}},W=async(e,t)=>{try{let o=r?"".concat(r,"/add/allowed_ip"):"/add/allowed_ip",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({ip:t})});if(!a.ok){let e=await a.text();throw Error("Network response was not ok: ".concat(e))}let n=await a.json();return console.log("addAllowedIP:",n),n}catch(e){throw console.error("Failed to add allowed IP:",e),e}},Q=async(e,t)=>{try{let o=r?"".concat(r,"/delete/allowed_ip"):"/delete/allowed_ip",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({ip:t})});if(!a.ok){let e=await a.text();throw Error("Network response was not ok: ".concat(e))}let n=await a.json();return console.log("deleteAllowedIP:",n),n}catch(e){throw console.error("Failed to delete allowed IP:",e),e}},ee=async(e,t,o,a,n,c,s,i)=>{try{let t=r?"".concat(r,"/model/metrics"):"/model/metrics";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(n,"&endTime=").concat(c,"&api_key=").concat(s,"&customer=").concat(i));let o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},et=async(e,t,o,a)=>{try{let n=r?"".concat(r,"/model/streaming_metrics"):"/model/streaming_metrics";t&&(n="".concat(n,"?_selected_model_group=").concat(t,"&startTime=").concat(o,"&endTime=").concat(a));let c=await fetch(n,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!c.ok){let e=await c.text();throw l(e),Error("Network response was not ok")}return await c.json()}catch(e){throw console.error("Failed to create key:",e),e}},eo=async(e,t,o,a,n,c,s,i)=>{try{let t=r?"".concat(r,"/model/metrics/slow_responses"):"/model/metrics/slow_responses";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(n,"&endTime=").concat(c,"&api_key=").concat(s,"&customer=").concat(i));let o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},ea=async(e,t,o,a,n,c,s,i)=>{try{let t=r?"".concat(r,"/model/metrics/exceptions"):"/model/metrics/exceptions";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(n,"&endTime=").concat(c,"&api_key=").concat(s,"&customer=").concat(i));let o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},er=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,c=(arguments.length>5&&void 0!==arguments[5]&&arguments[5],arguments.length>6&&void 0!==arguments[6]&&arguments[6]);console.log("in /models calls, globalLitellmHeaderName",d);try{let t=r?"".concat(r,"/models"):"/models",o=new URLSearchParams;o.append("include_model_access_groups","True"),!0===a&&o.append("return_wildcard_routes","True"),!0===c&&o.append("only_model_access_groups","True"),n&&o.append("team_id",n.toString()),o.toString()&&(t+="?".concat(o.toString()));let s=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok){let e=await s.text();throw l(e),Error("Network response was not ok")}return await s.json()}catch(e){throw console.error("Failed to create key:",e),e}},en=async e=>{try{let t=r?"".concat(r,"/global/spend/teams"):"/global/spend/teams";console.log("in teamSpendLogsCall:",t);let o=await fetch("".concat(t),{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ec=async(e,t,o,a)=>{try{let n=r?"".concat(r,"/global/spend/tags"):"/global/spend/tags";t&&o&&(n="".concat(n,"?start_date=").concat(t,"&end_date=").concat(o)),a&&(n+="".concat(n,"&tags=").concat(a.join(","))),console.log("in tagsSpendLogsCall:",n);let c=await fetch("".concat(n),{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!c.ok)throw await c.text(),Error("Network response was not ok");let s=await c.json();return console.log(s),s}catch(e){throw console.error("Failed to create key:",e),e}},es=async e=>{try{let t=r?"".concat(r,"/global/spend/all_tag_names"):"/global/spend/all_tag_names";console.log("in global/spend/all_tag_names call",t);let o=await fetch("".concat(t),{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ei=async e=>{try{let t=r?"".concat(r,"/global/all_end_users"):"/global/all_end_users";console.log("in global/all_end_users call",t);let o=await fetch("".concat(t),{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},el=async(e,t)=>{try{let o=r?"".concat(r,"/user/filter/ui"):"/user/filter/ui";t.get("user_email")&&(o+="?user_email=".concat(t.get("user_email"))),t.get("user_id")&&(o+="?user_id=".concat(t.get("user_id")));let a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to create key:",e),e}},ed=async(e,t,o,a,n,c,s,i,h,p,w)=>{try{let u=r?"".concat(r,"/spend/logs/ui"):"/spend/logs/ui",g=new URLSearchParams;t&&g.append("api_key",t),o&&g.append("team_id",o),a&&g.append("request_id",a),n&&g.append("start_date",n),c&&g.append("end_date",c),s&&g.append("page",s.toString()),i&&g.append("page_size",i.toString()),h&&g.append("user_id",h),p&&g.append("status_filter",p),w&&g.append("model",w);let f=g.toString();f&&(u+="?".concat(f));let y=await fetch(u,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!y.ok){let e=await y.text();throw l(e),Error("Network response was not ok")}let m=await y.json();return console.log("Spend Logs Response:",m),m}catch(e){throw console.error("Failed to fetch spend logs:",e),e}},eh=async e=>{try{let t=r?"".concat(r,"/global/spend/logs"):"/global/spend/logs",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ep=async e=>{try{let t=r?"".concat(r,"/global/spend/keys?limit=5"):"/global/spend/keys?limit=5",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ew=async(e,t,o,a)=>{try{let n=r?"".concat(r,"/global/spend/end_users"):"/global/spend/end_users",c="";c=t?JSON.stringify({api_key:t,startTime:o,endTime:a}):JSON.stringify({startTime:o,endTime:a});let s={method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:c},i=await fetch(n,s);if(!i.ok){let e=await i.text();throw l(e),Error("Network response was not ok")}let h=await i.json();return console.log(h),h}catch(e){throw console.error("Failed to create key:",e),e}},eu=async(e,t,o,a)=>{try{let n=r?"".concat(r,"/global/spend/provider"):"/global/spend/provider";o&&a&&(n+="?start_date=".concat(o,"&end_date=").concat(a)),t&&(n+="&api_key=".concat(t));let c={method:"GET",headers:{[d]:"Bearer ".concat(e)}},s=await fetch(n,c);if(!s.ok){let e=await s.text();throw l(e),Error("Network response was not ok")}let i=await s.json();return console.log(i),i}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eg=async(e,t,o)=>{try{let a=r?"".concat(r,"/global/activity"):"/global/activity";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let n={method:"GET",headers:{[d]:"Bearer ".concat(e)}},c=await fetch(a,n);if(!c.ok)throw await c.text(),Error("Network response was not ok");let s=await c.json();return console.log(s),s}catch(e){throw console.error("Failed to fetch spend data:",e),e}},ef=async(e,t,o)=>{try{let a=r?"".concat(r,"/global/activity/cache_hits"):"/global/activity/cache_hits";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let n={method:"GET",headers:{[d]:"Bearer ".concat(e)}},c=await fetch(a,n);if(!c.ok)throw await c.text(),Error("Network response was not ok");let s=await c.json();return console.log(s),s}catch(e){throw console.error("Failed to fetch spend data:",e),e}},ey=async(e,t,o)=>{try{let a=r?"".concat(r,"/global/activity/model"):"/global/activity/model";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let n={method:"GET",headers:{[d]:"Bearer ".concat(e)}},c=await fetch(a,n);if(!c.ok)throw await c.text(),Error("Network response was not ok");let s=await c.json();return console.log(s),s}catch(e){throw console.error("Failed to fetch spend data:",e),e}},em=async(e,t,o,a)=>{try{let n=r?"".concat(r,"/global/activity/exceptions"):"/global/activity/exceptions";t&&o&&(n+="?start_date=".concat(t,"&end_date=").concat(o)),a&&(n+="&model_group=".concat(a));let c={method:"GET",headers:{[d]:"Bearer ".concat(e)}},s=await fetch(n,c);if(!s.ok)throw await s.text(),Error("Network response was not ok");let i=await s.json();return console.log(i),i}catch(e){throw console.error("Failed to fetch spend data:",e),e}},ek=async(e,t,o,a)=>{try{let n=r?"".concat(r,"/global/activity/exceptions/deployment"):"/global/activity/exceptions/deployment";t&&o&&(n+="?start_date=".concat(t,"&end_date=").concat(o)),a&&(n+="&model_group=".concat(a));let c={method:"GET",headers:{[d]:"Bearer ".concat(e)}},s=await fetch(n,c);if(!s.ok)throw await s.text(),Error("Network response was not ok");let i=await s.json();return console.log(i),i}catch(e){throw console.error("Failed to fetch spend data:",e),e}},e_=async e=>{try{let t=r?"".concat(r,"/global/spend/models?limit=5"):"/global/spend/models?limit=5",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},eT=async(e,t)=>{try{let o=r?"".concat(r,"/v2/key/info"):"/v2/key/info",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({keys:t})});if(!a.ok){let e=await a.text();if(e.includes("Invalid proxy server token passed"))throw Error("Invalid proxy server token passed");throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log(n),n}catch(e){throw console.error("Failed to create key:",e),e}},ej=async(e,t,o)=>{try{console.log("Sending model connection test request:",JSON.stringify(t));let n=r?"".concat(r,"/health/test_connection"):"/health/test_connection",c=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json",[d]:"Bearer ".concat(e)},body:JSON.stringify({litellm_params:t,mode:o})}),s=c.headers.get("content-type");if(!s||!s.includes("application/json")){let e=await c.text();throw console.error("Received non-JSON response:",e),Error("Received non-JSON response (".concat(c.status,": ").concat(c.statusText,"). Check network tab for details."))}let i=await c.json();if(!c.ok||"error"===i.status){if("error"===i.status);else{var a;return{status:"error",message:(null===(a=i.error)||void 0===a?void 0:a.message)||"Connection test failed: ".concat(c.status," ").concat(c.statusText)}}}return i}catch(e){throw console.error("Model connection test error:",e),e}},eE=async(e,t)=>{try{console.log("entering keyInfoV1Call");let o=r?"".concat(r,"/key/info"):"/key/info";o="".concat(o,"?key=").concat(t);let n=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(console.log("response",n),!n.ok){let e=await n.text();l(e),a.ZP.error("Failed to fetch key info - "+e)}let c=await n.json();return console.log("data",c),c}catch(e){throw console.error("Failed to fetch key info:",e),e}},eC=async function(e,t,o,a,n,c,s,i){let h=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null,p=arguments.length>9&&void 0!==arguments[9]?arguments[9]:null;try{let w=r?"".concat(r,"/key/list"):"/key/list";console.log("in keyListCall");let u=new URLSearchParams;o&&u.append("team_id",o.toString()),t&&u.append("organization_id",t.toString()),a&&u.append("key_alias",a),c&&u.append("key_hash",c),n&&u.append("user_id",n.toString()),s&&u.append("page",s.toString()),i&&u.append("size",i.toString()),h&&u.append("sort_by",h),p&&u.append("sort_order",p),u.append("return_full_object","true"),u.append("include_team_keys","true");let g=u.toString();g&&(w+="?".concat(g));let f=await fetch(w,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!f.ok){let e=await f.text();throw l(e),Error("Network response was not ok")}let y=await f.json();return console.log("/team/list API Response:",y),y}catch(e){throw console.error("Failed to create key:",e),e}},eS=async(e,t)=>{try{let o=r?"".concat(r,"/user/get_users?role=").concat(t):"/user/get_users?role=".concat(t);console.log("in userGetAllUsersCall:",o);let a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log(n),n}catch(e){throw console.error("Failed to get requested models:",e),e}},eN=async e=>{try{let t=r?"".concat(r,"/user/available_roles"):"/user/available_roles",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");let a=await o.json();return console.log("response from user/available_role",a),a}catch(e){throw e}},ev=async(e,t)=>{try{if(console.log("Form Values in teamCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let o=r?"".concat(r,"/team/new"):"/team/new",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},eF=async(e,t)=>{try{if(console.log("Form Values in credentialCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let o=r?"".concat(r,"/credentials"):"/credentials",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},eb=async e=>{try{let t=r?"".concat(r,"/credentials"):"/credentials";console.log("in credentialListCall");let o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log("/credentials API Response:",a),a}catch(e){throw console.error("Failed to create key:",e),e}},ex=async(e,t,o)=>{try{let a=r?"".concat(r,"/credentials"):"/credentials";t?a+="/by_name/".concat(t):o&&(a+="/by_model/".concat(o)),console.log("in credentialListCall");let n=await fetch(a,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!n.ok){let e=await n.text();throw l(e),Error("Network response was not ok")}let c=await n.json();return console.log("/credentials API Response:",c),c}catch(e){throw console.error("Failed to create key:",e),e}},eB=async(e,t)=>{try{let o=r?"".concat(r,"/credentials/").concat(t):"/credentials/".concat(t);console.log("in credentialDeleteCall:",t);let a=await fetch(o,{method:"DELETE",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log(n),n}catch(e){throw console.error("Failed to delete key:",e),e}},eO=async(e,t,o)=>{try{if(console.log("Form Values in credentialUpdateCall:",o),o.metadata){console.log("formValues.metadata:",o.metadata);try{o.metadata=JSON.parse(o.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let a=r?"".concat(r,"/credentials/").concat(t):"/credentials/".concat(t),n=await fetch(a,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...o})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let c=await n.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to create key:",e),e}},eP=async(e,t)=>{try{if(console.log("Form Values in keyUpdateCall:",t),t.model_tpm_limit){console.log("formValues.model_tpm_limit:",t.model_tpm_limit);try{t.model_tpm_limit=JSON.parse(t.model_tpm_limit)}catch(e){throw Error("Failed to parse model_tpm_limit: "+e)}}if(t.model_rpm_limit){console.log("formValues.model_rpm_limit:",t.model_rpm_limit);try{t.model_rpm_limit=JSON.parse(t.model_rpm_limit)}catch(e){throw Error("Failed to parse model_rpm_limit: "+e)}}let o=r?"".concat(r,"/key/update"):"/key/update",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("Update key Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},eG=async(e,t)=>{try{console.log("Form Values in teamUpateCall:",t);let o=r?"".concat(r,"/team/update"):"/team/update",n=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error response from the server:",e),a.ZP.error("Failed to update team settings: "+e),Error(e)}let c=await n.json();return console.log("Update Team Response:",c),c}catch(e){throw console.error("Failed to update team:",e),e}},eJ=async(e,t,o)=>{try{console.log("Form Values in modelUpateCall:",t);let a=r?"".concat(r,"/model/").concat(o,"/update"):"/model/".concat(o,"/update"),n=await fetch(a,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error update from the server:",e),Error("Network response was not ok")}let c=await n.json();return console.log("Update model Response:",c),c}catch(e){throw console.error("Failed to update model:",e),e}},eA=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let n=r?"".concat(r,"/team/member_add"):"/team/member_add",c=await fetch(n,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_id:t,member:o})});if(!c.ok){var a;let e=await c.text(),t={};try{t=JSON.parse(e)}catch(t){console.warn("Failed to parse error body as JSON:",e)}let o=(null==t?void 0:null===(a=t.detail)||void 0===a?void 0:a.error)||"Failed to add team member",r=Error(o);throw r.raw=t,r}let s=await c.json();return console.log("API Response:",s),s}catch(e){throw console.error("Failed to create key:",e),e}},eI=async(e,t,o)=>{try{console.log("Form Values in teamMemberUpdateCall:",o);let n=r?"".concat(r,"/team/member_update"):"/team/member_update",c=await fetch(n,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_id:t,role:o.role,user_id:o.user_id})});if(!c.ok){var a;let e=await c.text(),t={};try{t=JSON.parse(e)}catch(t){console.warn("Failed to parse error body as JSON:",e)}let o=(null==t?void 0:null===(a=t.detail)||void 0===a?void 0:a.error)||"Failed to add team member",r=Error(o);throw r.raw=t,r}let s=await c.json();return console.log("API Response:",s),s}catch(e){throw console.error("Failed to update team member:",e),e}},eR=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let a=r?"".concat(r,"/team/member_delete"):"/team/member_delete",n=await fetch(a,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_id:t,...void 0!==o.user_email&&{user_email:o.user_email},...void 0!==o.user_id&&{user_id:o.user_id}})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let c=await n.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to create key:",e),e}},eU=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let a=r?"".concat(r,"/organization/member_add"):"/organization/member_add",n=await fetch(a,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,member:o})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error response from the server:",e),Error(e)}let c=await n.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to create organization member:",e),e}},ez=async(e,t,o)=>{try{console.log("Form Values in organizationMemberDeleteCall:",o);let a=r?"".concat(r,"/organization/member_delete"):"/organization/member_delete",n=await fetch(a,{method:"DELETE",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,user_id:o})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let c=await n.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to delete organization member:",e),e}},eV=async(e,t,o)=>{try{console.log("Form Values in organizationMemberUpdateCall:",o);let a=r?"".concat(r,"/organization/member_update"):"/organization/member_update",n=await fetch(a,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,...o})});if(!n.ok){let e=await n.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let c=await n.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to update organization member:",e),e}},eL=async(e,t,o)=>{try{console.log("Form Values in userUpdateUserCall:",t);let a=r?"".concat(r,"/user/update"):"/user/update",n={...t};null!==o&&(n.user_role=o),n=JSON.stringify(n);let c=await fetch(a,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:n});if(!c.ok){let e=await c.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let s=await c.json();return console.log("API Response:",s),s}catch(e){throw console.error("Failed to create key:",e),e}},eM=async(e,t)=>{try{let o=r?"".concat(r,"/health/services?service=").concat(t):"/health/services?service=".concat(t);console.log("Checking Slack Budget Alerts service health");let n=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!n.ok){let e=await n.text();throw l(e),Error(e)}let c=await n.json();return a.ZP.success("Test request to ".concat(t," made - check logs/alerts on ").concat(t," to verify")),c}catch(e){throw console.error("Failed to perform health check:",e),e}},eD=async e=>{try{let t=r?"".concat(r,"/budget/list"):"/budget/list",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},eZ=async(e,t,o)=>{try{let t=r?"".concat(r,"/get/config/callbacks"):"/get/config/callbacks",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},eH=async e=>{try{let t=r?"".concat(r,"/config/list?config_type=general_settings"):"/config/list?config_type=general_settings",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},eq=async e=>{try{let t=r?"".concat(r,"/config/pass_through_endpoint"):"/config/pass_through_endpoint",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},eY=async(e,t)=>{try{let o=r?"".concat(r,"/config/field/info?field_name=").concat(t):"/config/field/info?field_name=".concat(t),a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok)throw await a.text(),Error("Network response was not ok");return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},eX=async(e,t)=>{try{let o=r?"".concat(r,"/config/pass_through_endpoint"):"/config/pass_through_endpoint",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},e$=async(e,t,o)=>{try{let n=r?"".concat(r,"/config/field/update"):"/config/field/update",c=await fetch(n,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({field_name:t,field_value:o,config_type:"general_settings"})});if(!c.ok){let e=await c.text();throw l(e),Error("Network response was not ok")}let s=await c.json();return a.ZP.success("Successfully updated value!"),s}catch(e){throw console.error("Failed to set callbacks:",e),e}},eK=async(e,t)=>{try{let o=r?"".concat(r,"/config/field/delete"):"/config/field/delete",n=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({field_name:t,config_type:"general_settings"})});if(!n.ok){let e=await n.text();throw l(e),Error("Network response was not ok")}let c=await n.json();return a.ZP.success("Field reset on proxy"),c}catch(e){throw console.error("Failed to get callbacks:",e),e}},eW=async(e,t)=>{try{let o=r?"".concat(r,"/config/pass_through_endpoint?endpoint_id=").concat(t):"/config/pass_through_endpoint".concat(t),a=await fetch(o,{method:"DELETE",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},eQ=async(e,t)=>{try{let o=r?"".concat(r,"/config/update"):"/config/update",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},e0=async e=>{try{let t=r?"".concat(r,"/health"):"/health",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to call /health:",e),e}},e1=async(e,t)=>{try{let o=r?"".concat(r,"/health?model=").concat(encodeURIComponent(t)):"/health?model=".concat(encodeURIComponent(t)),a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw Error(e||"Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to call /health for model ".concat(t,":"),e),e}},e2=async e=>{try{let t=r?"".concat(r,"/cache/ping"):"/cache/ping",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error(e)}return await o.json()}catch(e){throw console.error("Failed to call /cache/ping:",e),e}},e3=async e=>{try{let t=r?"".concat(r,"/health/latest"):"/health/latest",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error(e)}return await o.json()}catch(e){throw console.error("Failed to call /health/latest:",e),e}},e4=async e=>{try{console.log("Getting proxy UI settings"),console.log("proxyBaseUrl in getProxyUISettings:",r);let t=r?"".concat(r,"/sso/get/ui_settings"):"/sso/get/ui_settings",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},e5=async e=>{try{let t=r?"".concat(r,"/v2/guardrails/list"):"/v2/guardrails/list",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get guardrails list:",e),e}},e6=async(e,t)=>{try{let o=r?"".concat(r,"/guardrails"):"/guardrails",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({guardrail:t})});if(!a.ok){let e=await a.text();throw l(e),Error(e)}let n=await a.json();return console.log("Create guardrail response:",n),n}catch(e){throw console.error("Failed to create guardrail:",e),e}},e8=async(e,t,o)=>{try{let a=r?"".concat(r,"/spend/logs/ui/").concat(t,"?start_date=").concat(encodeURIComponent(o)):"/spend/logs/ui/".concat(t,"?start_date=").concat(encodeURIComponent(o));console.log("Fetching log details from:",a);let n=await fetch(a,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!n.ok){let e=await n.text();throw l(e),Error("Network response was not ok")}let c=await n.json();return console.log("Fetched log details:",c),c}catch(e){throw console.error("Failed to fetch log details:",e),e}},e9=async e=>{try{let t=r?"".concat(r,"/get/internal_user_settings"):"/get/internal_user_settings";console.log("Fetching SSO settings from:",t);let o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched SSO settings:",a),a}catch(e){throw console.error("Failed to fetch SSO settings:",e),e}},e7=async(e,t)=>{try{let o=r?"".concat(r,"/update/internal_user_settings"):"/update/internal_user_settings";console.log("Updating internal user settings:",t);let n=await fetch(o,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!n.ok){let e=await n.text();throw l(e),Error("Network response was not ok")}let c=await n.json();return console.log("Updated internal user settings:",c),a.ZP.success("Internal user settings updated successfully"),c}catch(e){throw console.error("Failed to update internal user settings:",e),e}},te=async e=>{try{let t=r?"".concat(r,"/v1/mcp/server"):"/v1/mcp/server";console.log("Fetching MCP servers from:",t);let o=await fetch(t,{method:s.GET,headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched MCP servers:",a),a}catch(e){throw console.error("Failed to fetch MCP servers:",e),e}},tt=async(e,t)=>{try{console.log("Form Values in createMCPServer:",t);let o=r?"".concat(r,"/v1/mcp/server"):"/v1/mcp/server",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw l(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},to=async(e,t)=>{try{let o=r?"".concat(r,"/v1/mcp/server"):"/v1/mcp/server",a=await fetch(o,{method:"PUT",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to update MCP server:",e),e}},ta=async(e,t)=>{try{let o=(r?"".concat(r):"")+"/v1/mcp/server/".concat(t);console.log("in deleteMCPServer:",t);let a=await fetch(o,{method:s.DELETE,headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}}catch(e){throw console.error("Failed to delete key:",e),e}},tr=async(e,t)=>{try{let o=r?"".concat(r,"/mcp-rest/tools/list?server_id=").concat(t):"/mcp-rest/tools/list?server_id=".concat(t);console.log("Fetching MCP tools from:",o);let a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log("Fetched MCP tools:",n),n}catch(e){throw console.error("Failed to fetch MCP tools:",e),e}},tn=async(e,t,o,a)=>{try{let n=r?"".concat(r,"/mcp-rest/tools/call"):"/mcp-rest/tools/call";console.log("Calling MCP tool:",t,"with arguments:",o);let c=await fetch(n,{method:"POST",headers:{[d]:"Bearer ".concat(e),"x-mcp-auth":a,"Content-Type":"application/json"},body:JSON.stringify({name:t,arguments:o})});if(!c.ok){let e=await c.text();throw l(e),Error("Network response was not ok")}let s=await c.json();return console.log("MCP tool call response:",s),s}catch(e){throw console.error("Failed to call MCP tool:",e),e}},tc=async(e,t)=>{try{let o=r?"".concat(r,"/tag/new"):"/tag/new",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();await l(e);return}return await a.json()}catch(e){throw console.error("Error creating tag:",e),e}},ts=async(e,t)=>{try{let o=r?"".concat(r,"/tag/update"):"/tag/update",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();await l(e);return}return await a.json()}catch(e){throw console.error("Error updating tag:",e),e}},ti=async(e,t)=>{try{let o=r?"".concat(r,"/tag/info"):"/tag/info",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({names:t})});if(!a.ok){let e=await a.text();return await l(e),{}}return await a.json()}catch(e){throw console.error("Error getting tag info:",e),e}},tl=async e=>{try{let t=r?"".concat(r,"/tag/list"):"/tag/list",o=await fetch(t,{method:"GET",headers:{Authorization:"Bearer ".concat(e)}});if(!o.ok){let e=await o.text();return await l(e),{}}return await o.json()}catch(e){throw console.error("Error listing tags:",e),e}},td=async(e,t)=>{try{let o=r?"".concat(r,"/tag/delete"):"/tag/delete",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({name:t})});if(!a.ok){let e=await a.text();await l(e);return}return await a.json()}catch(e){throw console.error("Error deleting tag:",e),e}},th=async e=>{try{let t=r?"".concat(r,"/get/default_team_settings"):"/get/default_team_settings";console.log("Fetching default team settings from:",t);let o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched default team settings:",a),a}catch(e){throw console.error("Failed to fetch default team settings:",e),e}},tp=async(e,t)=>{try{let o=r?"".concat(r,"/update/default_team_settings"):"/update/default_team_settings";console.log("Updating default team settings:",t);let n=await fetch(o,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!n.ok){let e=await n.text();throw l(e),Error("Network response was not ok")}let c=await n.json();return console.log("Updated default team settings:",c),a.ZP.success("Default team settings updated successfully"),c}catch(e){throw console.error("Failed to update default team settings:",e),e}},tw=async(e,t)=>{try{let o=r?"".concat(r,"/team/permissions_list?team_id=").concat(t):"/team/permissions_list?team_id=".concat(t),a=await fetch(o,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log("Team permissions response:",n),n}catch(e){throw console.error("Failed to get team permissions:",e),e}},tu=async(e,t,o)=>{try{let a=r?"".concat(r,"/team/permissions_update"):"/team/permissions_update",n=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({team_id:t,team_member_permissions:o})});if(!n.ok){let e=await n.text();throw l(e),Error("Network response was not ok")}let c=await n.json();return console.log("Team permissions response:",c),c}catch(e){throw console.error("Failed to update team permissions:",e),e}},tg=async(e,t)=>{try{let o=r?"".concat(r,"/spend/logs/session/ui?session_id=").concat(encodeURIComponent(t)):"/spend/logs/session/ui?session_id=".concat(encodeURIComponent(t)),a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to fetch session logs:",e),e}},tf=async(e,t)=>{try{let o=r?"".concat(r,"/vector_store/new"):"/vector_store/new",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to create vector store")}return await a.json()}catch(e){throw console.error("Error creating vector store:",e),e}},ty=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let t=r?"".concat(r,"/vector_store/list"):"/vector_store/list",o=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!o.ok){let e=await o.json();throw Error(e.detail||"Failed to list vector stores")}return await o.json()}catch(e){throw console.error("Error listing vector stores:",e),e}},tm=async(e,t)=>{try{let o=r?"".concat(r,"/vector_store/delete"):"/vector_store/delete",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({vector_store_id:t})});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to delete vector store")}return await a.json()}catch(e){throw console.error("Error deleting vector store:",e),e}},tk=async e=>{try{let t=r?"".concat(r,"/email/event_settings"):"/email/event_settings",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Failed to get email event settings")}let a=await o.json();return console.log("Email event settings response:",a),a}catch(e){throw console.error("Failed to get email event settings:",e),e}},t_=async(e,t)=>{try{let o=r?"".concat(r,"/email/event_settings"):"/email/event_settings",a=await fetch(o,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw l(e),Error("Failed to update email event settings")}let n=await a.json();return console.log("Update email event settings response:",n),n}catch(e){throw console.error("Failed to update email event settings:",e),e}},tT=async e=>{try{let t=r?"".concat(r,"/email/event_settings/reset"):"/email/event_settings/reset",o=await fetch(t,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Failed to reset email event settings")}let a=await o.json();return console.log("Reset email event settings response:",a),a}catch(e){throw console.error("Failed to reset email event settings:",e),e}},tj=async(e,t)=>{try{let o=r?"".concat(r,"/guardrails/").concat(t):"/guardrails/".concat(t),a=await fetch(o,{method:"DELETE",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error(e)}let n=await a.json();return console.log("Delete guardrail response:",n),n}catch(e){throw console.error("Failed to delete guardrail:",e),e}},tE=async e=>{try{let t=r?"".concat(r,"/guardrails/ui/add_guardrail_settings"):"/guardrails/ui/add_guardrail_settings",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Failed to get guardrail UI settings")}let a=await o.json();return console.log("Guardrail UI settings response:",a),a}catch(e){throw console.error("Failed to get guardrail UI settings:",e),e}},tC=async e=>{try{let t=r?"".concat(r,"/guardrails/ui/provider_specific_params"):"/guardrails/ui/provider_specific_params",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Failed to get guardrail provider specific parameters")}let a=await o.json();return console.log("Guardrail provider specific params response:",a),a}catch(e){throw console.error("Failed to get guardrail provider specific parameters:",e),e}},tS=async(e,t)=>{try{let o=r?"".concat(r,"/guardrails/").concat(t,"/info"):"/guardrails/".concat(t,"/info"),a=await fetch(o,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw l(e),Error("Failed to get guardrail info")}let n=await a.json();return console.log("Guardrail info response:",n),n}catch(e){throw console.error("Failed to get guardrail info:",e),e}},tN=async(e,t,o)=>{try{let a=r?"".concat(r,"/guardrails/").concat(t):"/guardrails/".concat(t),n=await fetch(a,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok){let e=await n.text();throw l(e),Error("Failed to update guardrail")}let c=await n.json();return console.log("Update guardrail response:",c),c}catch(e){throw console.error("Failed to update guardrail:",e),e}},tv=async e=>{try{let t=r?"".concat(r,"/get/sso_settings"):"/get/sso_settings";console.log("Fetching SSO configuration from:",t);let o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw l(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched SSO configuration:",a),a}catch(e){throw console.error("Failed to fetch SSO configuration:",e),e}},tF=async(e,t)=>{try{let o=r?"".concat(r,"/update/sso_settings"):"/update/sso_settings";console.log("Updating SSO configuration:",t);let a=await fetch(o,{method:"PATCH",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}let n=await a.json();return console.log("Updated SSO configuration:",n),n}catch(e){throw console.error("Failed to update SSO configuration:",e),e}},tb=async(e,t,o,a,n)=>{try{let t=r?"".concat(r,"/audit"):"/audit",o=new URLSearchParams;a&&o.append("page",a.toString()),n&&o.append("page_size",n.toString());let c=o.toString();c&&(t+="?".concat(c));let s=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok){let e=await s.text();throw l(e),Error("Network response was not ok")}return await s.json()}catch(e){throw console.error("Failed to fetch audit logs:",e),e}},tx=async e=>{try{let t=r?"".concat(r,"/user/available_users"):"/user/available_users",o=await fetch(t,{method:"GET",headers:{[d]:"Bearer ".concat(e)}});if(!o.ok){if(404===o.status)return null;let e=await o.text();throw l(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to fetch remaining users:",e),e}},tB=async(e,t,o)=>{try{let n=r?"".concat(r,"/config/pass_through_endpoint/").concat(encodeURIComponent(t)):"/config/pass_through_endpoint/".concat(encodeURIComponent(t)),c=await fetch(n,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!c.ok){let e=await c.text();throw l(e),Error("Network response was not ok")}let s=await c.json();return a.ZP.success("Pass through endpoint updated successfully"),s}catch(e){throw console.error("Failed to update pass through endpoint:",e),e}},tO=async(e,t)=>{try{let o=r?"".concat(r,"/config/callback/delete"):"/config/callback/delete",a=await fetch(o,{method:"POST",headers:{[d]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({callback_name:t})});if(!a.ok){let e=await a.text();throw l(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to delete specific callback:",e),e}}}}]);